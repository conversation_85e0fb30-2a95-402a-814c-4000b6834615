/**
 * Comprehensive Shift Status Testing Script
 * Purpose: Test all shift scenarios using Windows PowerShell with .env credentials
 * Usage: node test/run-shift-status-tests.js
 */

require('dotenv').config();
const { Pool } = require('pg');

// Database configuration from .env
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function query(text, params) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

// Test scenarios
const testScenarios = [
  {
    name: 'Day Shift - Active Hours',
    startTime: '06:00:00',
    endTime: '18:00:00',
    testTime: '12:00:00',
    isOvernight: false,
    expectedWithinWindow: true,
    expectedLogic: 'simple_between_day'
  },
  {
    name: 'Day Shift - Outside Hours',
    startTime: '06:00:00',
    endTime: '18:00:00',
    testTime: '20:00:00',
    isOvernight: false,
    expectedWithinWindow: false,
    expectedLogic: 'simple_between_day'
  },
  {
    name: 'Night Shift - Before Midnight',
    startTime: '18:00:00',
    endTime: '06:00:00',
    testTime: '22:00:00',
    isOvernight: true,
    expectedWithinWindow: true,
    expectedLogic: 'dual_condition_overnight'
  },
  {
    name: 'Night Shift - After Midnight',
    startTime: '18:00:00',
    endTime: '06:00:00',
    testTime: '02:00:00',
    isOvernight: true,
    expectedWithinWindow: true,
    expectedLogic: 'dual_condition_overnight'
  },
  {
    name: 'Night Shift - Outside Hours',
    startTime: '18:00:00',
    endTime: '06:00:00',
    testTime: '12:00:00',
    isOvernight: true,
    expectedWithinWindow: false,
    expectedLogic: 'dual_condition_overnight'
  },
  {
    name: 'Boundary Test - Start Time',
    startTime: '06:00:00',
    endTime: '18:00:00',
    testTime: '06:00:00',
    isOvernight: false,
    expectedWithinWindow: true,
    expectedLogic: 'simple_between_day'
  },
  {
    name: 'Boundary Test - End Time',
    startTime: '06:00:00',
    endTime: '18:00:00',
    testTime: '18:00:00',
    isOvernight: false,
    expectedWithinWindow: true,
    expectedLogic: 'simple_between_day'
  },
  {
    name: 'Night Boundary - Start Time',
    startTime: '18:00:00',
    endTime: '06:00:00',
    testTime: '18:00:00',
    isOvernight: true,
    expectedWithinWindow: true,
    expectedLogic: 'dual_condition_overnight'
  },
  {
    name: 'Night Boundary - End Time',
    startTime: '18:00:00',
    endTime: '06:00:00',
    testTime: '06:00:00',
    isOvernight: true,
    expectedWithinWindow: true,
    expectedLogic: 'dual_condition_overnight'
  }
];

async function testShiftTimeLogic() {
  console.log('🧪 Testing Shift Time Logic...\n');
  
  let passed = 0;
  let failed = 0;

  for (const scenario of testScenarios) {
    try {
      const result = await query(`
        SELECT * FROM test_shift_time_logic($1, $2, $3, $4)
      `, [scenario.startTime, scenario.endTime, scenario.testTime, scenario.isOvernight]);

      const { is_overnight, is_within_window, logic_used } = result.rows[0];

      const success = 
        is_overnight === scenario.isOvernight &&
        is_within_window === scenario.expectedWithinWindow &&
        logic_used === scenario.expectedLogic;

      if (success) {
        console.log(`✅ ${scenario.name}`);
        passed++;
      } else {
        console.log(`❌ ${scenario.name}`);
        console.log(`   Expected: overnight=${scenario.isOvernight}, within=${scenario.expectedWithinWindow}, logic=${scenario.expectedLogic}`);
        console.log(`   Actual:   overnight=${is_overnight}, within=${is_within_window}, logic=${logic_used}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${scenario.name} - Error: ${error.message}`);
      failed++;
    }
  }

  console.log(`\n📊 Time Logic Tests: ${passed} passed, ${failed} failed\n`);
  return failed === 0;
}

async function testStatusEvaluation() {
  console.log('🔍 Testing Status Evaluation...\n');

  try {
    // Create test shift
    const shiftResult = await query(`
      INSERT INTO driver_shifts (
        truck_id, driver_id, shift_type, shift_date, start_date, end_date,
        start_time, end_time, status, recurrence_pattern
      ) VALUES (
        1, 1, 'day', CURRENT_DATE, CURRENT_DATE, CURRENT_DATE,
        '06:00:00', '18:00:00', 'scheduled', 'single'
      ) RETURNING id
    `);
    
    const testShiftId = shiftResult.rows[0].id;
    console.log(`📝 Created test shift ID: ${testShiftId}`);

    // Test status evaluation
    const statusResult = await query(`
      SELECT evaluate_shift_status($1, CURRENT_TIMESTAMP) as status
    `, [testShiftId]);
    
    const status = statusResult.rows[0].status;
    console.log(`✅ Status evaluation: ${status}`);

    // Test immutable status (completed)
    await query(`UPDATE driver_shifts SET status = 'completed' WHERE id = $1`, [testShiftId]);
    const completedResult = await query(`
      SELECT evaluate_shift_status($1, CURRENT_TIMESTAMP) as status
    `, [testShiftId]);
    
    if (completedResult.rows[0].status === 'completed') {
      console.log('✅ Immutable completed status respected');
    } else {
      console.log('❌ Immutable completed status not respected');
    }

    // Test immutable status (cancelled)
    await query(`UPDATE driver_shifts SET status = 'cancelled' WHERE id = $1`, [testShiftId]);
    const cancelledResult = await query(`
      SELECT evaluate_shift_status($1, CURRENT_TIMESTAMP) as status
    `, [testShiftId]);
    
    if (cancelledResult.rows[0].status === 'cancelled') {
      console.log('✅ Immutable cancelled status respected');
    } else {
      console.log('❌ Immutable cancelled status not respected');
    }

    // Clean up
    await query('DELETE FROM driver_shifts WHERE id = $1', [testShiftId]);
    console.log(`🗑️ Cleaned up test shift ID: ${testShiftId}`);

    return true;
  } catch (error) {
    console.log(`❌ Status evaluation test failed: ${error.message}`);
    return false;
  }
}

async function testBulkOperations() {
  console.log('📦 Testing Bulk Operations...\n');

  try {
    // Test bulk status update
    const startTime = Date.now();
    const updateResult = await query(`
      SELECT * FROM update_all_shift_statuses(CURRENT_TIMESTAMP)
    `);
    const updateDuration = Date.now() - startTime;

    const stats = updateResult.rows[0];
    console.log(`✅ Bulk update completed in ${updateDuration}ms`);
    console.log(`   Updated: ${stats.updated_count}, Activated: ${stats.activated_count}`);
    console.log(`   Completed: ${stats.completed_count}, Scheduled: ${stats.scheduled_count}`);

    // Test status summary
    const summaryStartTime = Date.now();
    const summaryResult = await query(`
      SELECT * FROM get_shift_status_summary(CURRENT_TIMESTAMP)
    `);
    const summaryDuration = Date.now() - summaryStartTime;

    const summary = summaryResult.rows[0];
    console.log(`✅ Status summary completed in ${summaryDuration}ms`);
    console.log(`   Total: ${summary.total_shifts}, Active: ${summary.active_shifts}`);
    console.log(`   Scheduled: ${summary.scheduled_shifts}, Completed: ${summary.completed_shifts}`);
    console.log(`   Needs activation: ${summary.needs_activation}, Needs completion: ${summary.needs_completion}`);
    console.log(`   Overnight active: ${summary.overnight_active}`);

    // Performance check
    if (updateDuration < 300 && summaryDuration < 100) {
      console.log('✅ Performance targets met');
      return true;
    } else {
      console.log('⚠️ Performance targets exceeded');
      return false;
    }
  } catch (error) {
    console.log(`❌ Bulk operations test failed: ${error.message}`);
    return false;
  }
}

async function testCrossSystemConsistency() {
  console.log('🔄 Testing Cross-System Consistency...\n');

  try {
    // Test assignment management integration
    const assignmentQuery = `
      SELECT 
        dt.truck_number,
        CASE 
          WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
            CONCAT('✅ ', ds.shift_type, ' Shift Active')
          ELSE '⚠️ No Active Shift'
        END as active_shift_status
      FROM dump_trucks dt
      LEFT JOIN driver_shifts ds ON (
        ds.truck_id = dt.id
        AND ds.status = 'active'
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        AND (
          (ds.end_time < ds.start_time AND 
           (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
          OR
          (ds.end_time >= ds.start_time AND 
           CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
        )
      )
      WHERE dt.status = 'active'
      LIMIT 5
    `;

    const assignmentResult = await query(assignmentQuery);
    console.log('✅ Assignment Management integration test completed');
    console.log(`   Tested ${assignmentResult.rows.length} trucks`);

    assignmentResult.rows.forEach(row => {
      console.log(`   ${row.truck_number}: ${row.active_shift_status}`);
    });

    return true;
  } catch (error) {
    console.log(`❌ Cross-system consistency test failed: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Comprehensive Shift Status Tests\n');
  console.log('=' .repeat(60));
  
  const results = [];

  try {
    // Test database connection
    await query('SELECT 1');
    console.log('✅ Database connection successful\n');

    // Run all test suites
    results.push(await testShiftTimeLogic());
    results.push(await testStatusEvaluation());
    results.push(await testBulkOperations());
    results.push(await testCrossSystemConsistency());

    // Summary
    console.log('=' .repeat(60));
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    if (passed === total) {
      console.log(`🎉 All tests passed! (${passed}/${total})`);
      console.log('✅ Enhanced Shift Status Management is working correctly');
    } else {
      console.log(`⚠️ Some tests failed (${passed}/${total})`);
      console.log('❌ Please review the failed tests above');
    }

  } catch (error) {
    console.error('❌ Test execution failed:', error);
  } finally {
    await pool.end();
  }
}

// Run tests if called directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests, testShiftTimeLogic, testStatusEvaluation, testBulkOperations, testCrossSystemConsistency };
