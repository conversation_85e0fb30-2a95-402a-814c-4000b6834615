# ============================================================================
# HAULING QR TRIP SYSTEM - DEPLOYMENT CONFIGURATION TEMPLATE
# Use this file for non-interactive deployments
# ============================================================================

# Repository Configuration
REPO_URL="https://github.com/yourusername/Hauling-QR-Trip-System.git"

# Server Configuration
DOMAIN_NAME="hauling.example.com"

# Database Configuration
DB_PASSWORD="YourSecurePassword123!@#"

# Security Configuration
JWT_SECRET="your-64-character-jwt-secret-key-here-make-it-very-long-and-random"

# Admin User Configuration
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="AdminPassword123!@#"

# SSL Configuration
ENABLE_SSL=true
SSL_EMAIL="<EMAIL>"

# Environment Configuration
ENVIRONMENT="production"

# Backup Configuration
BACKUP_SCHEDULE="daily"

# Logging Configuration
LOG_LEVEL="info"

# ============================================================================
# USAGE INSTRUCTIONS
# ============================================================================
#
# 1. Copy this file to your server:
#    scp config-template.conf user@your-server:/tmp/hauling-config.conf
#
# 2. Edit the configuration values:
#    nano /tmp/hauling-config.conf
#
# 3. Run deployment with config file:
#    ./deploy-hauling-qr.sh --config-file /tmp/hauling-config.conf
#
# ============================================================================
# SECURITY NOTES
# ============================================================================
#
# • Use strong passwords (minimum 12 characters, mixed case, numbers, symbols)
# • Generate a unique JWT secret (minimum 64 characters)
# • Use a valid domain name that points to your server
# • Provide a valid email address for SSL certificate notifications
# • Keep this configuration file secure and delete after deployment
#
# ============================================================================
