-- ============================================================================
-- Fix for Shift Status Logic Bug
-- Purpose: Correct the schedule_auto_activation function to properly handle overnight shifts
-- Date: 2025-07-15
-- ============================================================================

-- 1. First, create a fixed version of the schedule_auto_activation function
CREATE OR REPLACE FUNCTION schedule_auto_activation()
RETURNS void AS $$
DECLARE
    activated_count INTEGER := 0;
    completed_count INTEGER := 0;
BEGIN
    -- Auto-activate scheduled shifts that should be starting now
    UPDATE driver_shifts
    SET status = 'active', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'scheduled'
        AND (
            -- Date range check - shift is scheduled for today
            CURRENT_DATE BETWEEN start_date AND end_date
        )
        AND (
            -- For regular shifts (same day)
            (end_time >= start_time AND CURRENT_TIME >= start_time AND CURRENT_TIME < end_time)
            OR
            -- For overnight shifts (crosses midnight)
            (end_time < start_time AND (CURRENT_TIME >= start_time OR CURRENT_TIME < end_time))
        );

    GET DIAGNOSTICS activated_count = ROW_COUNT;

    -- Auto-complete active shifts that should be ending now
    -- FIXED: Properly handle multi-day overnight shifts by checking against end_date
    UPDATE driver_shifts
    SET status = 'completed', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active'
        AND (
            -- For regular shifts (same day)
            (
                end_time >= start_time AND 
                (
                    -- Either we're past the end_date
                    CURRENT_DATE > end_date OR
                    -- Or we're on the end_date and past the end_time
                    (CURRENT_DATE = end_date AND CURRENT_TIME >= end_time)
                )
            )
            OR
            -- For overnight shifts (crosses midnight)
            (
                end_time < start_time AND
                (
                    -- Either we're past the end_date + 1 day
                    CURRENT_DATE > (end_date + INTERVAL '1 day')::DATE OR
                    -- Or we're on end_date + 1 day and past the end_time
                    (CURRENT_DATE = (end_date + INTERVAL '1 day')::DATE AND CURRENT_TIME >= end_time)
                )
            )
        );

    GET DIAGNOSTICS completed_count = ROW_COUNT;

    -- Log the operation
    RAISE NOTICE 'Auto-activation completed: % activated, % completed', activated_count, completed_count;
END;
$$ LANGUAGE plpgsql;

-- 2. Reset incorrectly completed shifts
-- This will identify overnight shifts that were incorrectly marked as completed
-- and reset them to their correct status
DO $$
DECLARE
    reset_count INTEGER := 0;
BEGIN
    -- Reset overnight shifts that were incorrectly marked as completed
    UPDATE driver_shifts
    SET 
        status = 
            CASE 
                -- If current date/time is within the shift's window, mark as active
                WHEN 
                    CURRENT_DATE BETWEEN start_date AND end_date AND
                    (
                        -- For overnight shifts: active if current time is after start_time or before end_time
                        (end_time < start_time AND (CURRENT_TIME >= start_time OR CURRENT_TIME <= end_time))
                    )
                THEN 'active'
                -- Otherwise, mark as scheduled
                ELSE 'scheduled'
            END,
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        status = 'completed' AND
        shift_type = 'night' AND
        end_time < start_time AND
        -- Only reset shifts that should not be completed yet
        (
            -- Either we're before the end_date + 1 day
            CURRENT_DATE < (end_date + INTERVAL '1 day')::DATE OR
            -- Or we're on end_date + 1 day but before the end_time
            (CURRENT_DATE = (end_date + INTERVAL '1 day')::DATE AND CURRENT_TIME < end_time)
        );

    GET DIAGNOSTICS reset_count = ROW_COUNT;
    
    RAISE NOTICE 'Reset % incorrectly completed overnight shifts', reset_count;
END;
$$;

-- 3. Verify the fix by checking the status of our problematic shift
SELECT 
    id,
    truck_id,
    driver_id,
    shift_type,
    status AS current_status,
    start_date,
    end_date,
    start_time,
    end_time,
    CURRENT_DATE AS current_date,
    CURRENT_TIME AS current_time,
    CURRENT_TIMESTAMP AS current_timestamp,
    -- Calculate if the shift should be active based on date/time window
    CASE 
        WHEN CURRENT_DATE BETWEEN start_date AND end_date AND (
            -- For regular shifts (same day)
            (end_time >= start_time AND CURRENT_TIME BETWEEN start_time AND end_time)
            OR
            -- For overnight shifts (crosses midnight)
            (end_time < start_time AND (CURRENT_TIME >= start_time OR CURRENT_TIME <= end_time))
        ) THEN 'Should be ACTIVE'
        -- If current date is before start_date, or it's start_date but before start_time
        WHEN CURRENT_DATE < start_date OR 
             (CURRENT_DATE = start_date AND CURRENT_TIME < start_time) THEN 'Should be SCHEDULED'
        -- If current date is after end_date, or it's end_date but after end_time
        WHEN CURRENT_DATE > end_date OR 
             (CURRENT_DATE = end_date AND CURRENT_TIME > end_time) THEN 'Should be COMPLETED'
        ELSE 'NEEDS REVIEW'
    END AS expected_status
FROM 
    driver_shifts
WHERE 
    id = 536;