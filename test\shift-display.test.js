const request = require('supertest');
const express = require('express');
const { query } = require('../server/config/database');
const ShiftDisplayHelper = require('../server/utils/ShiftDisplayHelper');

// Mock database
jest.mock('../server/config/database', () => ({
  query: jest.fn(),
  getClient: jest.fn()
}));

describe('Shift Display Bug - Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    ShiftDisplayHelper.clearAllCache();
  });

  describe('Unit Tests - ShiftDisplayHelper', () => {
    test('should return <PERSON> for DT-100 night shift at 1:15 AM', async () => {
      const mockShift = {
        shift_id: 530,
        driver_id: 2,
        shift_type: 'night',
        display_type: 'night',
        driver_name: '<PERSON>',
        employee_id: 'DR-002'
      };

      query.mockResolvedValueOnce({
        rows: [mockShift]
      });

      const result = await ShiftDisplayHelper.getCurrentDriverForDisplay(1);
      
      expect(result.hasActiveShift).toBe(true);
      expect(result.driver_name).toBe('<PERSON> <PERSON>');
      expect(result.employee_id).toBe('DR-002');
      expect(result.shift_type).toBe('night');
    });

    test('should handle overnight shift time ranges correctly', async () => {
      // Mock current time as 1:15 AM for night shift 18:00-06:00
      const mockShift = {
        shift_id: 530,
        driver_id: 2,
        shift_type: 'night',
        display_type: 'night',
        driver_name: 'Maria Garcia',
        employee_id: 'DR-002',
        start_time: '18:00:00',
        end_time: '06:00:00'
      };

      query.mockResolvedValueOnce({
        rows: [mockShift]
      });

      const result = await ShiftDisplayHelper.getCurrentDriverForDisplay(1);
      
      expect(result.hasActiveShift).toBe(true);
    });

    test('should return no active shift when outside time range', async () => {
      query.mockResolvedValueOnce({
        rows: []
      });

      const result = await ShiftDisplayHelper.getCurrentDriverForDisplay(1);
      
      expect(result.hasActiveShift).toBe(false);
      expect(result.driver_name).toBe(null);
    });

    test('should handle day shift correctly', async () => {
      const mockShift = {
        shift_id: 531,
        driver_id: 2,
        shift_type: 'day',
        display_type: 'day',
        driver_name: 'Maria Garcia',
        employee_id: 'DR-002',
        start_time: '06:00:00',
        end_time: '18:00:00'
      };

      query.mockResolvedValueOnce({
        rows: [mockShift]
      });

      const result = await ShiftDisplayHelper.getCurrentDriverForDisplay(1);
      
      expect(result.hasActiveShift).toBe(true);
      expect(result.shift_type).toBe('day');
    });
  });

  describe('Integration Tests - API Endpoints', () => {
    let app;

    beforeAll(() => {
      app = express();
      app.use(express.json());
      
      // Mock auth middleware
      app.use((req, res, next) => {
        req.user = { id: 1 };
        next();
      });

      // Import routes
      const shiftsRouter = require('../server/routes/shifts');
      app.use('/api/shifts', shiftsRouter);
    });

    test('GET /api/shifts/current/1 should return Maria Garcia for active night shift', async () => {
      const mockShift = {
        id: 530,
        truck_id: 1,
        driver_id: 2,
        shift_type: 'night',
        shift_date: '2025-07-14',
        start_time: '18:00:00',
        end_time: '06:00:00',
        status: 'active',
        truck_number: 'DT-100',
        driver_name: 'Maria Garcia',
        employee_id: 'DR-002',
        assignment_code: 'TRK-001'
      };

      query.mockResolvedValueOnce({
        rows: [mockShift]
      });

      const response = await request(app)
        .get('/api/shifts/current/1')
        .set('Authorization', 'Bearer test-token');

      expect(response.status).toBe(200);
      expect(response.body.data.driver_name).toBe('Maria Garcia');
      expect(response.body.data.employee_id).toBe('DR-002');
      expect(response.body.data.shift_type).toBe('night');
    });

    test('GET /api/shifts/current/1 should return 404 when no active shift', async () => {
      query.mockResolvedValueOnce({
        rows: []
      });

      const response = await request(app)
        .get('/api/shifts/current/1')
        .set('Authorization', 'Bearer test-token');

      expect(response.status).toBe(404);
    });
  });

  describe('Cache Invalidation Tests', () => {
    test('should invalidate cache on shift status change', async () => {
      const mockShift = {
        shift_id: 530,
        driver_id: 2,
        shift_type: 'night',
        driver_name: 'Maria Garcia',
        employee_id: 'DR-002'
      };

      query.mockResolvedValueOnce({
        rows: [mockShift]
      });

      // First call - should populate cache
      await ShiftDisplayHelper.getCurrentDriverForDisplay(1);
      
      // Verify cache is populated
      expect(ShiftDisplayHelper.driverCache.size).toBeGreaterThan(0);

      // Simulate shift status change
      ShiftDisplayHelper.clearAllCache();
      
      // Verify cache is cleared
      expect(ShiftDisplayHelper.driverCache.size).toBe(0);
    });
  });
});