require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function checkMigrationStructure() {
  try {
    console.log('Checking migration_log structure:');
    const result = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'migration_log'
      ORDER BY ordinal_position
    `);
    result.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type}`);
    });

    console.log('\nChecking migrations structure:');
    const result2 = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'migrations'
      ORDER BY ordinal_position
    `);
    result2.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type}`);
    });

    console.log('\nSample data from migration_log:');
    const sample = await pool.query(`SELECT * FROM migration_log LIMIT 3`);
    console.log(sample.rows);

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await pool.end();
  }
}

checkMigrationStructure();
