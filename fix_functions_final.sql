-- Fix the functions to use proper timestamp casting

-- 1. Fix the fix_incorrectly_completed_shifts function
CREATE OR REPLACE FUNCTION fix_incorrectly_completed_shifts()
RETURNS TABLE(
    shift_id INTEGER,
    old_status shift_status,
    new_status shift_status,
    truck_id INTEGER,
    driver_id INTEGER,
    shift_type shift_type,
    start_date DATE,
    end_date DATE
) AS $$
DECLARE
    v_shift RECORD;
    v_correct_status shift_status;
    v_fixed_count INTEGER := 0;
BEGIN
    -- Find all completed shifts and check if they should actually be completed
    FOR v_shift IN
        SELECT ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status, ds.start_date, ds.end_date
        FROM driver_shifts ds
        WHERE ds.status = 'completed'
    LOOP
        -- Get the correct status using the override function
        v_correct_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP::TIMESTAMP, TRUE);
        
        -- If the correct status is different from current status, fix it
        IF v_correct_status != v_shift.status THEN
            UPDATE driver_shifts
            SET 
                status = v_correct_status,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = v_shift.id;
            
            v_fixed_count := v_fixed_count + 1;
            
            -- Return the fixed shift information
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_correct_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Fixed % incorrectly completed shifts', v_fixed_count;
END;
$$ LANGUAGE plpgsql;

-- 2. Fix the check_shift_status_consistency function
CREATE OR REPLACE FUNCTION check_shift_status_consistency()
RETURNS TABLE(
    shift_id INTEGER,
    current_status shift_status,
    expected_status shift_status,
    shift_truck_id INTEGER,
    shift_driver_id INTEGER,
    shift_type shift_type,
    start_date DATE,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    issue_description TEXT
) AS $$
DECLARE
    v_shift RECORD;
    v_expected_status shift_status;
BEGIN
    -- Check all shifts for status consistency
    FOR v_shift IN
        SELECT 
            ds.id, 
            ds.truck_id, 
            ds.driver_id, 
            ds.shift_type, 
            ds.status, 
            ds.start_date, 
            ds.end_date, 
            ds.start_time, 
            ds.end_time
        FROM driver_shifts ds
        WHERE ds.status IN ('scheduled', 'active', 'completed')
    LOOP
        -- Get the expected status using the override function
        v_expected_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP::TIMESTAMP, TRUE);
        
        -- If there's a mismatch, report it
        IF v_expected_status != v_shift.status THEN
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_expected_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date,
                v_shift.start_time,
                v_shift.end_time,
                CASE 
                    WHEN v_shift.status = 'completed' AND v_expected_status != 'completed' THEN 
                        'Shift incorrectly marked as completed'
                    WHEN v_shift.status = 'scheduled' AND v_expected_status = 'active' THEN 
                        'Shift should be active but is still scheduled'
                    WHEN v_shift.status = 'active' AND v_expected_status = 'completed' THEN 
                        'Shift should be completed but is still active'
                    ELSE 
                        'Status mismatch detected'
                END;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;