#!/usr/bin/env node

/**
 * Fix Shift Synchronization Issue
 * Clears cache and forces refresh of shift display data
 */

const ShiftDisplayHelper = require('../server/utils/ShiftDisplayHelper');
const { query } = require('../server/config/database');

async function fixShiftSync() {
  console.log('🔄 Fixing Shift Synchronization...');
  
  try {
    // Clear all caches
    ShiftDisplayHelper.clearAllCache();
    console.log('✅ Cache cleared');
    
    // Force refresh by querying current drivers
    const trucks = await query('SELECT id, truck_number FROM dump_trucks WHERE truck_number = $1', ['DT-100']);
    
    if (trucks.rows.length > 0) {
      const truckId = trucks.rows[0].id;
      const driverInfo = await ShiftDisplayHelper.getCurrentDriverForDisplay(truckId);
      
      console.log('📋 Current Driver Info:', {
        truck: trucks.rows[0].truck_number,
        hasActiveShift: driverInfo.hasActiveShift,
        driver: driverInfo.driver_name,
        shiftType: driverInfo.shift_type
      });
      
      if (driverInfo.hasActiveShift) {
        console.log('✅ Shift synchronization fixed! Active shift now recognized.');
      } else {
        console.log('⚠️  No active shift found - this may be expected if outside shift hours');
      }
    }
    
    console.log('🎉 Shift sync fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing shift sync:', error);
    process.exit(1);
  }
}

// Run the fix
fixShiftSync();