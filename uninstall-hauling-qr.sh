#!/bin/bash

# ============================================================================
# HAULING QR TRIP SYSTEM - UNINSTALL SCRIPT
# Complete removal with data preservation options
# ============================================================================

set -euo pipefail

# Configuration
APP_DIR="/opt/hauling-qr-system"
APP_USER="hauling"
DB_NAME="hauling_qr_system"
DB_USER="hauling_user"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Options
PRESERVE_DATA=false
PRESERVE_DATABASE=false
FORCE_REMOVE=false

# Logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "ERROR")   echo -e "${RED}❌ $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "INFO")    echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Show help
show_help() {
    echo "Hauling QR Trip System - Uninstall Script"
    echo
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  --preserve-data         Keep application data and logs"
    echo "  --preserve-database     Keep database and user"
    echo "  --force                 Force removal without confirmation"
    echo
    echo "Examples:"
    echo "  $0                      Complete removal with confirmation"
    echo "  $0 --preserve-data      Remove application but keep data"
    echo "  $0 --preserve-database  Remove application but keep database"
    echo "  $0 --force              Force removal without prompts"
    echo
}

# Parse arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --preserve-data)
                PRESERVE_DATA=true
                shift
                ;;
            --preserve-database)
                PRESERVE_DATABASE=true
                shift
                ;;
            --force)
                FORCE_REMOVE=true
                shift
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Confirmation prompt
confirm_removal() {
    if [[ "$FORCE_REMOVE" == true ]]; then
        return 0
    fi
    
    echo -e "${YELLOW}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                           UNINSTALL WARNING                                 ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    echo "This will remove the Hauling QR Trip System from your server."
    echo
    echo "What will be removed:"
    echo "• PM2 processes and configuration"
    echo "• Nginx configuration"
    echo "• Application files and code"
    echo "• Application user account"
    
    if [[ "$PRESERVE_DATABASE" == false ]]; then
        echo "• PostgreSQL database and user"
    fi
    
    if [[ "$PRESERVE_DATA" == false ]]; then
        echo "• Application logs and data"
        echo "• Backup files"
    fi
    
    echo
    echo "What will be preserved:"
    echo "• System packages (Node.js, PostgreSQL, Nginx)"
    echo "• SSL certificates"
    echo "• Firewall configuration"
    
    if [[ "$PRESERVE_DATABASE" == true ]]; then
        echo "• Database and user (--preserve-database)"
    fi
    
    if [[ "$PRESERVE_DATA" == true ]]; then
        echo "• Application data and logs (--preserve-data)"
    fi
    
    echo
    read -p "Are you sure you want to proceed? (type 'yes' to confirm): " confirmation
    
    if [[ "$confirmation" != "yes" ]]; then
        log "INFO" "Uninstall cancelled"
        exit 0
    fi
}

# Stop services
stop_services() {
    log "INFO" "Stopping services..."
    
    # Stop PM2 processes
    if command -v pm2 >/dev/null 2>&1; then
        if sudo -u "$APP_USER" pm2 list 2>/dev/null | grep -q "hauling-qr-system"; then
            log "INFO" "Stopping PM2 processes..."
            sudo -u "$APP_USER" pm2 delete hauling-qr-system 2>/dev/null || true
            sudo -u "$APP_USER" pm2 save 2>/dev/null || true
        fi
    fi
    
    # Remove PM2 startup script
    if [[ -f "/etc/systemd/system/pm2-$APP_USER.service" ]]; then
        log "INFO" "Removing PM2 startup service..."
        sudo systemctl stop "pm2-$APP_USER" 2>/dev/null || true
        sudo systemctl disable "pm2-$APP_USER" 2>/dev/null || true
        sudo rm -f "/etc/systemd/system/pm2-$APP_USER.service"
        sudo systemctl daemon-reload
    fi
    
    log "SUCCESS" "Services stopped"
}

# Remove Nginx configuration
remove_nginx_config() {
    log "INFO" "Removing Nginx configuration..."
    
    # Remove site configuration
    sudo rm -f "/etc/nginx/sites-available/hauling-qr-system"
    sudo rm -f "/etc/nginx/sites-enabled/hauling-qr-system"
    
    # Test Nginx configuration
    if sudo nginx -t 2>/dev/null; then
        sudo systemctl reload nginx
        log "SUCCESS" "Nginx configuration removed"
    else
        log "WARNING" "Nginx configuration test failed after removal"
    fi
}

# Remove application files
remove_application() {
    log "INFO" "Removing application files..."
    
    if [[ "$PRESERVE_DATA" == true ]]; then
        # Create backup of important data
        local backup_dir="/tmp/hauling-qr-backup-$(date +%Y%m%d_%H%M%S)"
        log "INFO" "Creating data backup at $backup_dir..."
        
        sudo mkdir -p "$backup_dir"
        
        # Backup logs
        if [[ -d "$APP_DIR/logs" ]]; then
            sudo cp -r "$APP_DIR/logs" "$backup_dir/"
        fi
        
        # Backup any custom configurations
        if [[ -f "$APP_DIR/.env" ]]; then
            sudo cp "$APP_DIR/.env" "$backup_dir/"
        fi
        
        # Backup any uploaded files
        if [[ -d "$APP_DIR/uploads" ]]; then
            sudo cp -r "$APP_DIR/uploads" "$backup_dir/"
        fi
        
        # Backup database backups
        if [[ -d "$APP_DIR/backups" ]]; then
            sudo cp -r "$APP_DIR/backups" "$backup_dir/"
        fi
        
        sudo chown -R "$USER:$USER" "$backup_dir"
        log "SUCCESS" "Data backed up to $backup_dir"
    fi
    
    # Remove application directory
    if [[ -d "$APP_DIR" ]]; then
        sudo rm -rf "$APP_DIR"
        log "SUCCESS" "Application directory removed"
    fi
}

# Remove application user
remove_user() {
    log "INFO" "Removing application user..."
    
    if id "$APP_USER" >/dev/null 2>&1; then
        # Remove user and home directory
        sudo userdel -r "$APP_USER" 2>/dev/null || true
        log "SUCCESS" "Application user removed"
    else
        log "INFO" "Application user does not exist"
    fi
}

# Remove database
remove_database() {
    if [[ "$PRESERVE_DATABASE" == true ]]; then
        log "INFO" "Preserving database (--preserve-database option)"
        return 0
    fi
    
    log "INFO" "Removing database..."
    
    # Drop database and user
    sudo -u postgres psql -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null || true
    sudo -u postgres psql -c "DROP USER IF EXISTS $DB_USER;" 2>/dev/null || true
    
    log "SUCCESS" "Database removed"
}

# Clean up system
cleanup_system() {
    log "INFO" "Performing system cleanup..."
    
    # Remove any remaining cron jobs
    sudo -u "$APP_USER" crontab -r 2>/dev/null || true
    
    # Clean up any temporary files
    sudo rm -f /tmp/hauling-qr-*
    
    log "SUCCESS" "System cleanup completed"
}

# Main uninstall function
main_uninstall() {
    log "INFO" "Starting Hauling QR Trip System uninstall..."
    
    # Check if application exists
    if [[ ! -d "$APP_DIR" ]] && ! id "$APP_USER" >/dev/null 2>&1; then
        log "WARNING" "Hauling QR Trip System does not appear to be installed"
        exit 0
    fi
    
    # Confirm removal
    confirm_removal
    
    # Execute removal steps
    stop_services
    remove_nginx_config
    remove_application
    remove_user
    remove_database
    cleanup_system
    
    # Show completion message
    echo -e "\n${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                        UNINSTALL COMPLETED                                  ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    log "SUCCESS" "Hauling QR Trip System has been removed from your server"
    
    if [[ "$PRESERVE_DATA" == true ]]; then
        log "INFO" "Application data has been preserved in /tmp/hauling-qr-backup-*"
    fi
    
    if [[ "$PRESERVE_DATABASE" == true ]]; then
        log "INFO" "Database has been preserved"
    fi
    
    echo
    echo "Remaining system components:"
    echo "• Node.js, PostgreSQL, Nginx (system packages)"
    echo "• SSL certificates (if configured)"
    echo "• Firewall rules"
    echo
    echo "To completely remove these components, run:"
    echo "sudo apt remove --purge nodejs postgresql nginx-*"
    echo "sudo ufw --force reset"
    echo
}

# Main execution
main() {
    parse_arguments "$@"
    main_uninstall
}

main "$@"
