require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function markMigrationExecuted() {
  try {
    await pool.query(`
      INSERT INTO migration_log (migration_name, executed_at, description)
      VALUES ('024_fix_shift_constraint', CURRENT_TIMESTAMP, 'Fix shift constraint for multi-driver support')
      ON CONFLICT (migration_name) DO NOTHING
    `);
    console.log('✅ Marked 024_fix_shift_constraint.sql as executed');
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

markMigrationExecuted();
