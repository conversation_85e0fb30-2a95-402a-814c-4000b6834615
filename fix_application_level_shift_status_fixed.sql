-- ============================================================================
-- Application-Level Shift Status Fix (Fixed)
-- Purpose: Fix the evaluate_shift_status function to allow recalculation and prevent immutable status issues
-- Date: 2025-07-15
-- ============================================================================

-- 1. Create an enhanced version of evaluate_shift_status that allows override
CREATE OR REPLACE FUNCTION evaluate_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    p_allow_override BOOLEAN DEFAULT FALSE
)
RETURNS shift_status AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_is_past_completion BOOLEAN;
    v_new_status shift_status;
BEGIN
    -- Get shift details
    SELECT
        id,
        start_date,
        end_date,
        start_time,
        end_time,
        shift_type,
        status,
        recurrence_pattern
    INTO v_shift
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN 'scheduled'::shift_status;
    END IF;

    -- FIXED: Allow override of completed status when explicitly requested
    IF NOT p_allow_override AND v_shift.status IN ('completed', 'cancelled') THEN
        RETURN v_shift.status;
    END IF;

    -- Extract current date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;

    -- Check if shift spans overnight (night shift logic)
    v_is_overnight := v_shift.end_time < v_shift.start_time;

    -- Enhanced date range validation with unified approach
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;

    -- Enhanced time window validation with proper overnight logic
    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic for overnight spans
        v_is_within_time_window := (v_current_time >= v_shift.start_time OR v_current_time <= v_shift.end_time);
    ELSE
        -- Day shift: Use simple BETWEEN logic for same-day shifts
        v_is_within_time_window := (v_current_time BETWEEN v_shift.start_time AND v_shift.end_time);
    END IF;

    -- FIXED: Enhanced completion logic with proper overnight handling
    IF v_is_overnight THEN
        -- For overnight shifts: completed when we're past end_time on the next day
        v_is_past_completion := p_reference_timestamp > ((v_shift.end_date + INTERVAL '1 day')::DATE + v_shift.end_time);
    ELSE
        -- For day shifts: completed when past end_time on the same day
        v_is_past_completion := p_reference_timestamp > (v_shift.end_date::DATE + v_shift.end_time);
    END IF;

    -- Apply status rules in priority order
    IF v_is_past_completion THEN
        -- Rule 3: Completed - past the shift's end datetime
        v_new_status := 'completed';
    ELSIF v_is_within_date_range AND v_is_within_time_window THEN
        -- Rule 1: Active - within date range AND within time window
        v_new_status := 'active';
    ELSIF v_is_within_date_range AND NOT v_is_within_time_window THEN
        -- Rule 2: Scheduled - within date range BUT outside time window
        v_new_status := 'scheduled';
    ELSE
        -- Default: Scheduled for future dates
        v_new_status := 'scheduled';
    END IF;

    RETURN v_new_status;
END;
$$ LANGUAGE plpgsql;

-- 2. Create a function to detect and fix incorrectly completed shifts
CREATE OR REPLACE FUNCTION fix_incorrectly_completed_shifts()
RETURNS TABLE(
    shift_id INTEGER,
    old_status shift_status,
    new_status shift_status,
    truck_id INTEGER,
    driver_id INTEGER,
    shift_type shift_type,
    start_date DATE,
    end_date DATE
) AS $$
DECLARE
    v_shift RECORD;
    v_correct_status shift_status;
    v_fixed_count INTEGER := 0;
BEGIN
    -- Find all completed shifts and check if they should actually be completed
    FOR v_shift IN
        SELECT ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status, ds.start_date, ds.end_date
        FROM driver_shifts ds
        WHERE ds.status = 'completed'
    LOOP
        -- Get the correct status using the override function
        v_correct_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP, TRUE);
        
        -- If the correct status is different from current status, fix it
        IF v_correct_status != v_shift.status THEN
            UPDATE driver_shifts
            SET 
                status = v_correct_status,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = v_shift.id;
            
            v_fixed_count := v_fixed_count + 1;
            
            -- Return the fixed shift information
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_correct_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Fixed % incorrectly completed shifts', v_fixed_count;
END;
$$ LANGUAGE plpgsql;

-- 3. Create a monitoring function to check for status inconsistencies
CREATE OR REPLACE FUNCTION check_shift_status_consistency()
RETURNS TABLE(
    shift_id INTEGER,
    current_status shift_status,
    expected_status shift_status,
    shift_truck_id INTEGER,
    shift_driver_id INTEGER,
    shift_type shift_type,
    start_date DATE,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    issue_description TEXT
) AS $$
DECLARE
    v_shift RECORD;
    v_expected_status shift_status;
BEGIN
    -- Check all shifts for status consistency
    FOR v_shift IN
        SELECT ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status, ds.start_date, ds.end_date, ds.start_time, ds.end_time
        FROM driver_shifts ds
        WHERE ds.status IN ('scheduled', 'active', 'completed')
    LOOP
        -- Get the expected status using the override function
        v_expected_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP, TRUE);
        
        -- If there's a mismatch, report it
        IF v_expected_status != v_shift.status THEN
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_expected_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date,
                v_shift.start_time,
                v_shift.end_time,
                CASE 
                    WHEN v_shift.status = 'completed' AND v_expected_status != 'completed' THEN 
                        'Shift incorrectly marked as completed'
                    WHEN v_shift.status = 'scheduled' AND v_expected_status = 'active' THEN 
                        'Shift should be active but is still scheduled'
                    WHEN v_shift.status = 'active' AND v_expected_status = 'completed' THEN 
                        'Shift should be completed but is still active'
                    ELSE 
                        'Status mismatch detected'
                END;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 4. Test the fixes
DO $$
BEGIN
    RAISE NOTICE 'Testing shift status fixes...';
    
    -- Check for inconsistencies before fixing
    RAISE NOTICE 'Checking for status inconsistencies...';
    PERFORM check_shift_status_consistency();
    
    -- Fix incorrectly completed shifts
    RAISE NOTICE 'Fixing incorrectly completed shifts...';
    PERFORM fix_incorrectly_completed_shifts();
    
    -- Check for inconsistencies after fixing
    RAISE NOTICE 'Checking for remaining inconsistencies...';
    PERFORM check_shift_status_consistency();
    
    RAISE NOTICE 'Shift status fixes completed successfully';
END;
$$;