# 🚀 Hauling QR Trip System - Automated Deployment Guide

## 📋 Overview

This comprehensive deployment package provides fully automated installation, management, and monitoring scripts for the Hauling QR Trip System on Ubuntu 24.04 VPS servers.

## 📦 Package Contents

### Core Deployment Scripts
- **`deploy-hauling-qr.sh`** - Main automated deployment script
- **`uninstall-hauling-qr.sh`** - Complete system removal script
- **`health-monitor.sh`** - Comprehensive health monitoring script
- **`config-template.conf`** - Configuration template for non-interactive deployments

### Generated Maintenance Scripts (Created during deployment)
- **`backup.sh`** - Automated backup script
- **`health-check.sh`** - Quick health check script
- **`update.sh`** - Safe application update script

## 🎯 Quick Start

### Interactive Deployment (Recommended)
```bash
# Download the deployment script
wget https://raw.githubusercontent.com/yourusername/Hauling-QR-Trip-System/main/deploy-hauling-qr.sh

# Make it executable
chmod +x deploy-hauling-qr.sh

# Run interactive deployment
./deploy-hauling-qr.sh
```

### Non-Interactive Deployment
```bash
# Download configuration template
wget https://raw.githubusercontent.com/yourusername/Hauling-QR-Trip-System/main/config-template.conf

# Edit configuration
nano config-template.conf

# Run deployment with config file
./deploy-hauling-qr.sh --config-file config-template.conf
```

## 🔧 System Requirements

### Minimum Specifications
- **OS**: Ubuntu 24.04 LTS
- **RAM**: 2GB minimum, 4GB recommended
- **CPU**: 2 cores minimum
- **Disk**: 20GB available space
- **Network**: Public IP with domain name (for SSL)

### Required Access
- **SSH access** with sudo privileges
- **Domain name** pointing to server IP
- **Email address** for SSL certificate notifications

## 📚 Detailed Usage

### Main Deployment Script Options

```bash
./deploy-hauling-qr.sh [OPTIONS]

Options:
  -h, --help              Show help message
  -v, --verbose           Enable verbose output
  -q, --quiet             Minimal output
  --dry-run               Preview without making changes
  --config-file FILE      Use configuration file
  --skip-ssl              Skip SSL certificate setup
  --resume-from STEP      Resume from specific step (1-11)
  --rollback-to STEP      Rollback to checkpoint
  --clean-install         Remove existing installation
```

### Deployment Steps Overview

1. **System Preparation** - Update packages, install dependencies
2. **Node.js Installation** - Install Node.js 18.x from NodeSource
3. **PostgreSQL Setup** - Install and configure PostgreSQL 15
4. **Application Setup** - Create user, clone repository
5. **Environment Config** - Generate production .env file
6. **Dependencies** - Install npm packages
7. **Database Init** - Execute schema and create admin user
8. **Frontend Build** - Build React application for production
9. **Nginx Config** - Configure reverse proxy and static serving
10. **Process Management** - Setup PM2 clustering
11. **Security** - Configure firewall and SSL certificates

### Health Monitoring

```bash
# Basic health check
./health-monitor.sh

# Detailed health check
./health-monitor.sh --verbose

# JSON output for monitoring systems
./health-monitor.sh --json

# Alert mode (exits with error code if issues found)
./health-monitor.sh --alert
```

### System Removal

```bash
# Complete removal with confirmation
./uninstall-hauling-qr.sh

# Preserve database
./uninstall-hauling-qr.sh --preserve-database

# Preserve application data
./uninstall-hauling-qr.sh --preserve-data

# Force removal without prompts
./uninstall-hauling-qr.sh --force
```

## 🔐 Security Features

### Automated Security Configuration
- **UFW Firewall** - Minimal required ports (22, 80, 443)
- **Fail2ban** - SSH brute force protection
- **SSL/TLS** - Automatic Let's Encrypt certificates
- **Security Headers** - Comprehensive HTTP security headers
- **Database Security** - Isolated database user with minimal privileges

### Password Requirements
- **Minimum 12 characters**
- **Mixed case letters**
- **Numbers and symbols**
- **Automatic strength validation**

## 📊 Monitoring & Maintenance

### Health Monitoring Thresholds
- **CPU Usage**: Warning at 80%
- **Memory Usage**: Warning at 80%
- **Disk Usage**: Critical at 85%
- **API Response Time**: Warning at 5000ms

### Automated Backups
- **Database backups** with compression
- **Application file backups** (excluding node_modules)
- **Configurable retention** (7 days default)
- **Cron job automation** (daily/weekly)

### Log Management
- **Application logs**: `/opt/hauling-qr-system/logs/`
- **Health monitoring**: `/var/log/hauling-qr-health.log`
- **Nginx logs**: `/var/log/nginx/`
- **PostgreSQL logs**: `/var/log/postgresql/`

## 🛠️ Post-Deployment Management

### Common Commands
```bash
# View application status
sudo -u hauling pm2 status

# View application logs
sudo -u hauling pm2 logs hauling-qr-system

# Restart application
sudo -u hauling pm2 restart hauling-qr-system

# Run health check
/opt/hauling-qr-system/scripts/health-check.sh

# Create backup
/opt/hauling-qr-system/scripts/backup.sh

# Update application
/opt/hauling-qr-system/scripts/update.sh
```

### Service Management
```bash
# Check service status
sudo systemctl status postgresql nginx

# Restart services
sudo systemctl restart postgresql nginx

# View service logs
sudo journalctl -u postgresql -f
sudo journalctl -u nginx -f
```

## 🚨 Troubleshooting

### Common Issues

#### Deployment Fails at Database Step
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connection
sudo -u postgres psql -l

# Restart PostgreSQL
sudo systemctl restart postgresql
```

#### Application Won't Start
```bash
# Check PM2 logs
sudo -u hauling pm2 logs hauling-qr-system

# Check environment variables
sudo -u hauling pm2 env 0

# Restart with fresh environment
sudo -u hauling pm2 restart hauling-qr-system --update-env
```

#### Frontend Not Loading
```bash
# Check Nginx status
sudo systemctl status nginx

# Test Nginx configuration
sudo nginx -t

# Check build files
ls -la /opt/hauling-qr-system/client/build/
```

#### SSL Certificate Issues
```bash
# Check certificate status
sudo certbot certificates

# Renew certificates manually
sudo certbot renew

# Test automatic renewal
sudo certbot renew --dry-run
```

### Recovery Options

#### Resume Failed Deployment
```bash
# Resume from last checkpoint
./deploy-hauling-qr.sh --resume-from 5

# Clean install (removes existing)
./deploy-hauling-qr.sh --clean-install
```

#### Rollback Changes
```bash
# Rollback to specific checkpoint
./deploy-hauling-qr.sh --rollback-to 3

# Complete removal
./uninstall-hauling-qr.sh
```

## 📞 Support & Documentation

### Log Files
- **Deployment log**: `/tmp/hauling-qr-deploy-YYYYMMDD_HHMMSS.log`
- **Application logs**: `/opt/hauling-qr-system/logs/`
- **Health monitoring**: `/var/log/hauling-qr-health.log`

### Configuration Files
- **Application config**: `/opt/hauling-qr-system/.env`
- **Nginx config**: `/etc/nginx/sites-available/hauling-qr-system`
- **PM2 config**: `/opt/hauling-qr-system/ecosystem.config.js`

### Useful Commands
```bash
# View deployment log
tail -f /tmp/hauling-qr-deploy-*.log

# Monitor system resources
htop

# Check network connectivity
curl -I http://localhost/health

# View database tables
sudo -u hauling psql -d hauling_qr_system -c "\dt"
```

## 🔄 Updates & Maintenance

### Regular Maintenance Tasks
1. **Weekly**: Run health checks and review logs
2. **Monthly**: Update system packages and review security
3. **Quarterly**: Review backup retention and test restore procedures
4. **Annually**: Renew SSL certificates (automated) and security audit

### Update Procedure
```bash
# Automated update (recommended)
/opt/hauling-qr-system/scripts/update.sh

# Manual update
cd /opt/hauling-qr-system
sudo -u hauling git pull origin main
sudo -u hauling npm install --production
sudo -u hauling pm2 restart hauling-qr-system
```

---

## 📄 License

This deployment package is part of the Hauling QR Trip System and is licensed under the MIT License.

## 🤝 Contributing

For issues, improvements, or contributions to the deployment scripts, please submit issues or pull requests to the main repository.

---

**🎉 Happy Deploying!**
