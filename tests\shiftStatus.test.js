const ShiftStatusService = require('../server/services/shiftStatusService');

describe('ShiftStatusService', () => {
    describe('evaluateShiftStatus', () => {
        test('should return active for day shift within time window', () => {
            const shift = {
                start_date: new Date('2025-07-14'),
                end_date: new Date('2025-07-31'),
                start_time: '09:00:00',
                end_time: '17:00:00',
                shift_type: 'day'
            };
            
            const status = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-14T14:00:00Z')
            );
            
            expect(status).toBe('active');
        });

        test('should return scheduled for day shift outside time window', () => {
            const shift = {
                start_date: new Date('2025-07-14'),
                end_date: new Date('2025-07-31'),
                start_time: '09:00:00',
                end_time: '17:00:00',
                shift_type: 'day'
            };
            
            const status = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-14T08:00:00Z')
            );
            
            expect(status).toBe('scheduled');
        });

        test('should return completed for shift past end date', () => {
            const shift = {
                start_date: new Date('2025-07-01'),
                end_date: new Date('2025-07-10'),
                start_time: '09:00:00',
                end_time: '17:00:00',
                shift_type: 'day'
            };
            
            const status = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-15T12:00:00Z')
            );
            
            expect(status).toBe('completed');
        });

        test('should handle overnight night shifts correctly', () => {
            const shift = {
                start_date: new Date('2025-07-14'),
                end_date: new Date('2025-07-31'),
                start_time: '18:00:00',
                end_time: '06:00:00',
                shift_type: 'night'
            };
            
            // Should be active at 22:00 (within night shift)
            const status1 = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-14T22:00:00Z')
            );
            expect(status1).toBe('active');
            
            // Should be active at 02:00 next day (overnight)
            const status2 = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-15T02:00:00Z')
            );
            expect(status2).toBe('active');
            
            // Should be scheduled at 12:00 (outside night shift)
            const status3 = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-14T12:00:00Z')
            );
            expect(status3).toBe('scheduled');
        });

        test('should handle edge cases at exact start/end times', () => {
            const shift = {
                start_date: new Date('2025-07-14'),
                end_date: new Date('2025-07-31'),
                start_time: '10:00:00',
                end_time: '18:00:00',
                shift_type: 'day'
            };
            
            // Exactly at start time should be active
            const status1 = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-14T10:00:00Z')
            );
            expect(status1).toBe('active');
            
            // Exactly at end time should be scheduled
            const status2 = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-14T18:00:00Z')
            );
            expect(status2).toBe('scheduled');
        });
    });

    describe('time parsing', () => {
        test('should correctly parse time strings', () => {
            const shift = {
                start_date: new Date('2025-07-14'),
                end_date: new Date('2025-07-31'),
                start_time: '09:30:45',
                end_time: '17:45:15',
                shift_type: 'day'
            };
            
            const status = ShiftStatusService.evaluateShiftStatus(
                shift, 
                new Date('2025-07-14T14:00:00Z')
            );
            
            expect(status).toBe('active');
        });
    });
});

// Integration tests (requires database)
describe('ShiftStatusService Integration', () => {
    // These tests require a database connection
    // Skip if no database is available
    
    if (process.env.NODE_ENV === 'test') {
        test.skip('should update shift status in database', async () => {
            // This would require a test database setup
            // const result = await ShiftStatusService.updateShiftStatus(1);
            // expect(result.success).toBe(true);
        });
        
        test.skip('should get shifts needing updates', async () => {
            // const needingUpdates = await ShiftStatusService.getShiftsNeedingStatusUpdate();
            // expect(Array.isArray(needingUpdates)).toBe(true);
        });
    }
});