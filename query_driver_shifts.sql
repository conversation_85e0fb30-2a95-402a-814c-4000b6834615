-- Query to examine current driver_shifts records with status, dates, times, and current timestamp
SELECT 
    id,
    truck_id,
    driver_id,
    shift_type,
    status,
    start_date,
    end_date,
    start_time,
    end_time,
    shift_date,
    CURRENT_DATE AS current_date,
    CURRENT_TIME AS current_time,
    CURRENT_TIMESTAMP AS current_timestamp,
    -- Calculate if the shift should be active based on date/time window
    CASE 
        WHEN CURRENT_DATE BETWEEN start_date AND end_date AND (
            -- For regular shifts (same day)
            (end_time >= start_time AND CURRENT_TIME BETWEEN start_time AND end_time)
            OR
            -- For overnight shifts (crosses midnight)
            (end_time < start_time AND (CURRENT_TIME >= start_time OR CURRENT_TIME <= end_time))
        ) THEN 'Should be ACTIVE'
        -- If current date is before start_date, or it's start_date but before start_time
        WHEN CURRENT_DATE < start_date OR 
             (CURRENT_DATE = start_date AND CURRENT_TIME < start_time) THEN 'Should be SCHEDULED'
        -- If current date is after end_date, or it's end_date but after end_time
        WHEN CURRENT_DATE > end_date OR 
             (CURRENT_DATE = end_date AND CURRENT_TIME > end_time) THEN 'Should be COMPLETED'
        ELSE 'NEEDS REVIEW'
    END AS expected_status
FROM 
    driver_shifts
ORDER BY 
    start_date DESC, start_time DESC;