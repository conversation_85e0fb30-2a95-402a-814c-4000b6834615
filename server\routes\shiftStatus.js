const express = require('express');
const router = express.Router();
const ShiftStatusService = require('../services/shiftStatusService');

/**
 * @route   POST /api/shift-status/update/:shiftId
 * @desc    Update status for a specific shift
 * @access  Private (Admin/Supervisor)
 */
router.post('/update/:shiftId', async (req, res) => {
    try {
        const { shiftId } = req.params;
        const { referenceTime } = req.body;
        
        // Validate shiftId
        if (!shiftId || isNaN(shiftId)) {
            return res.status(400).json({
                success: false,
                message: 'Valid shift ID is required'
            });
        }
        
        // Parse reference time if provided
        let refTime = new Date();
        if (referenceTime) {
            refTime = new Date(referenceTime);
            if (isNaN(refTime.getTime())) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid reference time format'
                });
            }
        }
        
        const result = await ShiftStatusService.updateShiftStatus(
            parseInt(shiftId), 
            refTime
        );
        
        res.json({
            success: true,
            data: result
        });
        
    } catch (error) {
        console.error('Error updating shift status:', error);
        
        if (error.message === 'Shift not found') {
            return res.status(404).json({
                success: false,
                message: 'Shift not found'
            });
        }
        
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route   POST /api/shift-status/update-all
 * @desc    Update status for all shifts
 * @access  Private (Admin/Supervisor)
 */
router.post('/update-all', async (req, res) => {
    try {
        const { referenceTime, dryRun } = req.body;
        
        // Parse reference time if provided
        let refTime = new Date();
        if (referenceTime) {
            refTime = new Date(referenceTime);
            if (isNaN(refTime.getTime())) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid reference time format'
                });
            }
        }
        
        if (dryRun) {
            // Return shifts that would be updated without making changes
            const needingUpdates = await ShiftStatusService.getShiftsNeedingStatusUpdate(refTime);
            
            res.json({
                success: true,
                data: {
                    shiftsNeedingUpdate: needingUpdates,
                    count: needingUpdates.length,
                    isDryRun: true
                }
            });
        } else {
            // Perform actual updates
            const result = await ShiftStatusService.updateAllShiftStatuses(refTime);
            
            res.json({
                success: true,
                data: result
            });
        }
        
    } catch (error) {
        console.error('Error updating all shift statuses:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route   GET /api/shift-status/evaluate/:shiftId
 * @desc    Evaluate what status a shift should have without updating
 * @access  Private (Admin/Supervisor)
 */
router.get('/evaluate/:shiftId', async (req, res) => {
    try {
        const { shiftId } = req.params;
        const { referenceTime } = req.query;
        
        // Validate shiftId
        if (!shiftId || isNaN(shiftId)) {
            return res.status(400).json({
                success: false,
                message: 'Valid shift ID is required'
            });
        }
        
        // Parse reference time if provided
        let refTime = new Date();
        if (referenceTime) {
            refTime = new Date(referenceTime);
            if (isNaN(refTime.getTime())) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid reference time format'
                });
            }
        }
        
        const client = await ShiftStatusService.pool.connect();
        
        try {
            // Get shift details
            const shiftQuery = `
                SELECT id, start_date, end_date, start_time, end_time, shift_type, status
                FROM driver_shifts
                WHERE id = $1
            `;
            const shiftResult = await client.query(shiftQuery, [shiftId]);
            
            if (shiftResult.rows.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Shift not found'
                });
            }
            
            const shift = shiftResult.rows[0];
            const evaluatedStatus = ShiftStatusService.evaluateShiftStatus(shift, refTime);
            
            res.json({
                success: true,
                data: {
                    shiftId: parseInt(shiftId),
                    currentStatus: shift.status,
                    evaluatedStatus,
                    shiftDetails: shift,
                    referenceTime: refTime.toISOString()
                }
            });
            
        } finally {
            client.release();
        }
        
    } catch (error) {
        console.error('Error evaluating shift status:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route   GET /api/shift-status/needs-update
 * @desc    Get all shifts that need status updates
 * @access  Private (Admin/Supervisor)
 */
router.get('/needs-update', async (req, res) => {
    try {
        const { referenceTime } = req.query;
        
        // Parse reference time if provided
        let refTime = new Date();
        if (referenceTime) {
            refTime = new Date(referenceTime);
            if (isNaN(refTime.getTime())) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid reference time format'
                });
            }
        }
        
        const needingUpdates = await ShiftStatusService.getShiftsNeedingStatusUpdate(refTime);
        
        res.json({
            success: true,
            data: {
                shiftsNeedingUpdate: needingUpdates,
                count: needingUpdates.length,
                referenceTime: refTime.toISOString()
            }
        });
        
    } catch (error) {
        console.error('Error getting shifts needing updates:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

module.exports = router;