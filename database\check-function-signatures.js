require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function checkFunctionSignatures() {
  try {
    console.log('Checking enhanced shift status function signatures:');
    
    // Check update_all_shift_statuses parameters
    const updateParams = await pool.query(`
      SELECT
        data_type,
        parameter_name,
        parameter_mode
      FROM information_schema.parameters
      WHERE specific_name IN (
        SELECT specific_name
        FROM information_schema.routines
        WHERE routine_name = 'update_all_shift_statuses'
      )
      ORDER BY ordinal_position
    `);
    
    console.log('\nupdate_all_shift_statuses parameters:');
    if (updateParams.rows.length > 0) {
      updateParams.rows.forEach(row => {
        console.log(`- ${row.parameter_name}: ${row.data_type} (${row.parameter_mode})`);
      });
    } else {
      console.log('- No parameters (function takes no arguments)');
    }

    // Check evaluate_shift_status parameters
    const evalParams = await pool.query(`
      SELECT
        data_type,
        parameter_name,
        parameter_mode
      FROM information_schema.parameters
      WHERE specific_name IN (
        SELECT specific_name
        FROM information_schema.routines
        WHERE routine_name = 'evaluate_shift_status'
      )
      ORDER BY ordinal_position
    `);
    
    console.log('\nevaluate_shift_status parameters:');
    if (evalParams.rows.length > 0) {
      evalParams.rows.forEach(row => {
        console.log(`- ${row.parameter_name}: ${row.data_type} (${row.parameter_mode})`);
      });
    } else {
      console.log('- No parameters (function takes no arguments)');
    }

    // Test the functions with correct signatures
    console.log('\nTesting function calls:');
    
    try {
      const updateResult = await pool.query('SELECT * FROM update_all_shift_statuses()');
      console.log('✅ update_all_shift_statuses() - SUCCESS');
    } catch (error) {
      console.log('❌ update_all_shift_statuses() - FAILED:', error.message);
    }

    try {
      const evalResult = await pool.query('SELECT evaluate_shift_status(1) as result');
      console.log('✅ evaluate_shift_status(1) - SUCCESS');
    } catch (error) {
      console.log('❌ evaluate_shift_status(1) - FAILED:', error.message);
    }

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await pool.end();
  }
}

checkFunctionSignatures();
