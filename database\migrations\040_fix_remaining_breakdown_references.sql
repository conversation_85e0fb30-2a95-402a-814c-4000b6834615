-- Migration: Fix remaining breakdown column references in views and indexes
-- Description: Updates all database objects that still reference old breakdown column names
-- Version: 029
-- Date: 2025-07-09

-- Drop and recreate views that reference old column names
DROP VIEW IF EXISTS v_trip_summary CASCADE;
DROP MATERIALIZED VIEW IF EXISTS mv_breakdown_analytics_summary CASCADE;
DROP MATERIALIZED VIEW IF EXISTS mv_fleet_performance_summary CASCADE;

-- Recreate v_trip_summary with stopped terminology
CREATE VIEW v_trip_summary AS
SELECT
    tl.id,
    a.assignment_code,
    a.assigned_date,
    dt.truck_number,
    d.full_name as driver_name,
    tl.trip_number,
    tl.status,
    tl.previous_status,
    tl.loading_start_time,
    tl.trip_completed_time,
    tl.total_duration_minutes,
    tl.loading_duration_minutes,
    tl.travel_duration_minutes,
    tl.unloading_duration_minutes,
    tl.is_exception,
    tl.exception_reason,
    tl.stopped_reported_at,
    tl.stopped_reason,
    tl.stopped_resolved_at,
    ll.name as loading_location,
    ul.name as unloading_location,
    COALESCE(al.name, ll.name) as actual_loading_location,
    COALESCE(aul.name, ul.name) as actual_unloading_location
FROM trip_logs tl
JOIN assignments a ON tl.assignment_id = a.id
JOIN dump_trucks dt ON a.truck_id = dt.id
JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
ORDER BY tl.created_at DESC;

-- Recreate stopped analytics summary (renamed from breakdown)
CREATE MATERIALIZED VIEW mv_stopped_analytics_summary AS
SELECT
  dt.truck_number,
  dt.id as truck_id,
  d.full_name as driver_name,
  
  -- Stopped counts and timing
  COUNT(*) as total_stopped,
  AVG(EXTRACT(EPOCH FROM (tl.stopped_resolved_at - tl.stopped_reported_at))/60) as avg_resolution_time_minutes,
  MAX(tl.stopped_reported_at) as last_stopped_date,
  
  -- Stopped by phase
  COUNT(CASE WHEN tl.previous_status = 'loading_start' THEN 1 END) as loading_phase_stopped,
  COUNT(CASE WHEN tl.previous_status = 'loading_end' THEN 1 END) as travel_to_unload_stopped,
  COUNT(CASE WHEN tl.previous_status = 'unloading_start' THEN 1 END) as unloading_phase_stopped,
  COUNT(CASE WHEN tl.previous_status = 'unloading_end' THEN 1 END) as travel_to_load_stopped,
  
  -- Stopped reasons
  MODE() WITHIN GROUP (ORDER BY tl.stopped_reason) as most_common_reason,
  COUNT(DISTINCT tl.stopped_reason) as unique_stopped_reasons,
  
  -- Time patterns
  MODE() WITHIN GROUP (ORDER BY EXTRACT(HOUR FROM tl.stopped_reported_at)) as most_common_hour,
  MODE() WITHIN GROUP (ORDER BY EXTRACT(DOW FROM tl.stopped_reported_at)) as most_common_day_of_week,
  
  -- Performance impact
  AVG(tl.total_duration_minutes) as avg_trip_duration_with_stopped,
  
  -- Update timestamp
  NOW() as last_updated

FROM trip_logs tl
JOIN assignments a ON tl.assignment_id = a.id
JOIN dump_trucks dt ON a.truck_id = dt.id
LEFT JOIN drivers d ON a.driver_id = d.id
WHERE tl.status = 'stopped'
  AND tl.stopped_reported_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY dt.truck_number, dt.id, d.full_name;

-- Recreate fleet performance summary with stopped terminology
CREATE MATERIALIZED VIEW mv_fleet_performance_summary AS
SELECT
  dt.truck_number,
  dt.id as truck_id,
  d.full_name as driver_name,
  ll.name as loading_location,
  ul.name as unloading_location,
  
  -- Time tracking
  CASE 
    WHEN tl.status = 'loading_start' AND tl.loading_start_time IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.loading_start_time))/60
    WHEN tl.status = 'loading_end' AND tl.loading_end_time IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.loading_end_time))/60
    WHEN tl.status = 'unloading_start' AND tl.unloading_start_time IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.unloading_start_time))/60
    WHEN tl.status = 'unloading_end' AND tl.unloading_end_time IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.unloading_end_time))/60
    WHEN tl.status = 'stopped' AND tl.stopped_reported_at IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.stopped_reported_at))/60
    ELSE 0
  END as time_in_current_phase_minutes,
  
  -- Performance indicators
  tl.total_duration_minutes,
  tl.is_exception,
  tl.stopped_reason,
  a.priority,
  a.is_adaptive,
  
  -- Update timestamp
  NOW() as last_updated

FROM trip_logs tl
JOIN assignments a ON tl.assignment_id = a.id
JOIN dump_trucks dt ON a.truck_id = dt.id
LEFT JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
WHERE tl.created_at >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY tl.created_at DESC;

-- Drop old indexes that reference breakdown columns
DROP INDEX IF EXISTS idx_trips_breakdown_analytics;
DROP INDEX IF EXISTS idx_trip_logs_breakdown;
DROP INDEX IF EXISTS idx_trip_logs_breakdown_date;

-- Create new indexes with stopped terminology
CREATE INDEX IF NOT EXISTS idx_trips_stopped_analytics 
ON trip_logs(status, stopped_reported_at, stopped_resolved_at, previous_status) 
WHERE status = 'stopped';

CREATE INDEX IF NOT EXISTS idx_trip_logs_stopped_status 
ON trip_logs(status) WHERE status = 'stopped';

CREATE INDEX IF NOT EXISTS idx_trip_logs_stopped_date 
ON trip_logs(stopped_reported_at) WHERE stopped_reported_at IS NOT NULL;

-- Update the current status index to use stopped instead of breakdown
DROP INDEX IF EXISTS idx_trips_current_status_time;
CREATE INDEX idx_trips_current_status_time 
ON trip_logs(status, created_at DESC) 
WHERE status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'stopped');

-- Add comments for new objects
COMMENT ON MATERIALIZED VIEW mv_stopped_analytics_summary IS 'Summary analytics for stopped trips (formerly breakdown analytics)';
COMMENT ON MATERIALIZED VIEW mv_fleet_performance_summary IS 'Fleet performance metrics with stopped terminology';

-- Refresh materialized views
REFRESH MATERIALIZED VIEW mv_stopped_analytics_summary;
REFRESH MATERIALIZED VIEW mv_fleet_performance_summary;

-- Verify the migration
DO $$
BEGIN
    -- Check if views exist
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_trip_summary') THEN
        RAISE NOTICE 'Migration verification: v_trip_summary recreated successfully';
    ELSE
        RAISE WARNING 'Migration verification: v_trip_summary not found';
    END IF;
    
    -- Check if materialized views exist
    IF EXISTS (SELECT 1 FROM pg_matviews WHERE matviewname = 'mv_stopped_analytics_summary') THEN
        RAISE NOTICE 'Migration verification: mv_stopped_analytics_summary created successfully';
    ELSE
        RAISE WARNING 'Migration verification: mv_stopped_analytics_summary not found';
    END IF;
    
    -- Check if old breakdown indexes are gone
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_trips_breakdown_analytics') THEN
        RAISE NOTICE 'Migration verification: Old breakdown indexes successfully removed';
    ELSE
        RAISE WARNING 'Migration verification: Some old breakdown indexes may still exist';
    END IF;
END $$;
