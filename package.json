{"name": "hauling-qr-trip-system", "version": "1.0.0", "description": "Hauling QR Trip Management System", "main": "server/index.js", "scripts": {"start": "node server/index.js", "dev": "nodemon server/index.js", "test": "jest", "db:migrate": "node database/run-migration.js"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "helmet": "^7.0.0", "express-rate-limit": "^6.7.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3"}, "keywords": ["hauling", "qr", "trip", "management", "logistics"], "author": "Hauling QR System Team", "license": "MIT"}