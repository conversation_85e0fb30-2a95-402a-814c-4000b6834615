/**
 * Comprehensive Shift Status Validation Tests
 * Purpose: Test enhanced shift status evaluation logic
 * Features: Day/night shifts, boundary conditions, overnight logic
 */

const { query } = require('../server/config/database');

describe('Enhanced Shift Status Evaluation', () => {
  let testShiftId;

  beforeAll(async () => {
    // Create a test shift for validation
    const result = await query(`
      INSERT INTO driver_shifts (
        truck_id, driver_id, shift_type, shift_date, start_date, end_date,
        start_time, end_time, status, recurrence_pattern
      ) VALUES (
        1, 1, 'day', CURRENT_DATE, CURRENT_DATE, CURRENT_DATE,
        '06:00:00', '18:00:00', 'scheduled', 'single'
      ) RETURNING id
    `);
    testShiftId = result.rows[0].id;
  });

  afterAll(async () => {
    // Clean up test shift
    if (testShiftId) {
      await query('DELETE FROM driver_shifts WHERE id = $1', [testShiftId]);
    }
  });

  describe('Day Shift Logic', () => {
    test('should be active during day shift hours', async () => {
      const result = await query(`
        SELECT test_shift_time_logic('06:00:00', '18:00:00', '12:00:00', false) as result
      `);
      
      const { is_overnight, is_within_window, logic_used } = result.rows[0].result;
      
      expect(is_overnight).toBe(false);
      expect(is_within_window).toBe(true);
      expect(logic_used).toBe('simple_between_day');
    });

    test('should be scheduled outside day shift hours', async () => {
      const result = await query(`
        SELECT test_shift_time_logic('06:00:00', '18:00:00', '20:00:00', false) as result
      `);
      
      const { is_overnight, is_within_window, logic_used } = result.rows[0].result;
      
      expect(is_overnight).toBe(false);
      expect(is_within_window).toBe(false);
      expect(logic_used).toBe('simple_between_day');
    });

    test('should handle boundary conditions correctly', async () => {
      // Test start time boundary
      const startResult = await query(`
        SELECT test_shift_time_logic('06:00:00', '18:00:00', '06:00:00', false) as result
      `);
      expect(startResult.rows[0].result.is_within_window).toBe(true);

      // Test end time boundary
      const endResult = await query(`
        SELECT test_shift_time_logic('06:00:00', '18:00:00', '18:00:00', false) as result
      `);
      expect(endResult.rows[0].result.is_within_window).toBe(true);
    });
  });

  describe('Night Shift Logic', () => {
    test('should be active during night shift hours (before midnight)', async () => {
      const result = await query(`
        SELECT test_shift_time_logic('18:00:00', '06:00:00', '22:00:00', true) as result
      `);
      
      const { is_overnight, is_within_window, logic_used } = result.rows[0].result;
      
      expect(is_overnight).toBe(true);
      expect(is_within_window).toBe(true);
      expect(logic_used).toBe('dual_condition_overnight');
    });

    test('should be active during night shift hours (after midnight)', async () => {
      const result = await query(`
        SELECT test_shift_time_logic('18:00:00', '06:00:00', '02:00:00', true) as result
      `);
      
      const { is_overnight, is_within_window, logic_used } = result.rows[0].result;
      
      expect(is_overnight).toBe(true);
      expect(is_within_window).toBe(true);
      expect(logic_used).toBe('dual_condition_overnight');
    });

    test('should be scheduled outside night shift hours', async () => {
      const result = await query(`
        SELECT test_shift_time_logic('18:00:00', '06:00:00', '12:00:00', true) as result
      `);
      
      const { is_overnight, is_within_window, logic_used } = result.rows[0].result;
      
      expect(is_overnight).toBe(true);
      expect(is_within_window).toBe(false);
      expect(logic_used).toBe('dual_condition_overnight');
    });

    test('should handle midnight boundary correctly', async () => {
      // Test start time boundary (18:00)
      const startResult = await query(`
        SELECT test_shift_time_logic('18:00:00', '06:00:00', '18:00:00', true) as result
      `);
      expect(startResult.rows[0].result.is_within_window).toBe(true);

      // Test end time boundary (06:00)
      const endResult = await query(`
        SELECT test_shift_time_logic('18:00:00', '06:00:00', '06:00:00', true) as result
      `);
      expect(endResult.rows[0].result.is_within_window).toBe(true);
    });
  });

  describe('Shift Status Evaluation', () => {
    test('should evaluate status correctly for current time', async () => {
      const result = await query(`
        SELECT evaluate_shift_status($1, CURRENT_TIMESTAMP) as status
      `, [testShiftId]);
      
      const status = result.rows[0].status;
      expect(['scheduled', 'active', 'completed']).toContain(status);
    });

    test('should respect immutable completed status', async () => {
      // Set shift to completed
      await query(`
        UPDATE driver_shifts SET status = 'completed' WHERE id = $1
      `, [testShiftId]);

      const result = await query(`
        SELECT evaluate_shift_status($1, CURRENT_TIMESTAMP) as status
      `, [testShiftId]);
      
      expect(result.rows[0].status).toBe('completed');

      // Reset for other tests
      await query(`
        UPDATE driver_shifts SET status = 'scheduled' WHERE id = $1
      `, [testShiftId]);
    });

    test('should respect immutable cancelled status', async () => {
      // Set shift to cancelled
      await query(`
        UPDATE driver_shifts SET status = 'cancelled' WHERE id = $1
      `, [testShiftId]);

      const result = await query(`
        SELECT evaluate_shift_status($1, CURRENT_TIMESTAMP) as status
      `, [testShiftId]);
      
      expect(result.rows[0].status).toBe('cancelled');

      // Reset for other tests
      await query(`
        UPDATE driver_shifts SET status = 'scheduled' WHERE id = $1
      `, [testShiftId]);
    });
  });

  describe('Bulk Status Updates', () => {
    test('should update multiple shifts correctly', async () => {
      const result = await query(`
        SELECT * FROM update_all_shift_statuses(CURRENT_TIMESTAMP)
      `);
      
      const stats = result.rows[0];
      expect(typeof stats.updated_count).toBe('number');
      expect(typeof stats.activated_count).toBe('number');
      expect(typeof stats.completed_count).toBe('number');
      expect(typeof stats.scheduled_count).toBe('number');
    });

    test('should provide status summary', async () => {
      const result = await query(`
        SELECT * FROM get_shift_status_summary(CURRENT_TIMESTAMP)
      `);
      
      const summary = result.rows[0];
      expect(typeof summary.total_shifts).toBe('number');
      expect(typeof summary.active_shifts).toBe('number');
      expect(typeof summary.scheduled_shifts).toBe('number');
      expect(typeof summary.completed_shifts).toBe('number');
      expect(typeof summary.needs_activation).toBe('number');
      expect(typeof summary.needs_completion).toBe('number');
      expect(typeof summary.overnight_active).toBe('number');
    });
  });

  describe('Edge Cases', () => {
    test('should handle non-existent shift ID', async () => {
      const result = await query(`
        SELECT evaluate_shift_status(99999, CURRENT_TIMESTAMP) as status
      `);
      
      expect(result.rows[0].status).toBe('error');
    });

    test('should handle invalid timestamps gracefully', async () => {
      const result = await query(`
        SELECT evaluate_shift_status($1, '2025-01-01 12:00:00') as status
      `, [testShiftId]);
      
      const status = result.rows[0].status;
      expect(['scheduled', 'active', 'completed']).toContain(status);
    });
  });

  describe('Performance Tests', () => {
    test('should complete status evaluation within performance target', async () => {
      const startTime = Date.now();
      
      await query(`
        SELECT * FROM update_all_shift_statuses(CURRENT_TIMESTAMP)
      `);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(300); // 300ms target
    });

    test('should handle large number of shifts efficiently', async () => {
      const startTime = Date.now();
      
      // Test with status summary which processes all shifts
      await query(`
        SELECT * FROM get_shift_status_summary(CURRENT_TIMESTAMP)
      `);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(100); // Should be very fast for summary
    });
  });
});

module.exports = {
  testShiftStatusLogic: async () => {
    console.log('🧪 Running shift status validation tests...');
    
    try {
      // Test day shift logic
      console.log('Testing day shift logic...');
      const dayResult = await query(`
        SELECT test_shift_time_logic('06:00:00', '18:00:00', '12:00:00', false) as result
      `);
      console.log('Day shift test:', dayResult.rows[0].result);

      // Test night shift logic
      console.log('Testing night shift logic...');
      const nightResult = await query(`
        SELECT test_shift_time_logic('18:00:00', '06:00:00', '22:00:00', true) as result
      `);
      console.log('Night shift test:', nightResult.rows[0].result);

      // Test status summary
      console.log('Testing status summary...');
      const summaryResult = await query(`
        SELECT * FROM get_shift_status_summary(CURRENT_TIMESTAMP)
      `);
      console.log('Status summary:', summaryResult.rows[0]);

      console.log('✅ All shift status validation tests passed!');
      return true;
    } catch (error) {
      console.error('❌ Shift status validation tests failed:', error);
      return false;
    }
  }
};
