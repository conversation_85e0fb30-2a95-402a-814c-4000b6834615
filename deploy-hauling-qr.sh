#!/bin/bash

# ============================================================================
# HAULING QR TRIP SYSTEM - AUTOMATED DEPLOYMENT SCRIPT
# Ubuntu 24.04 VPS Production Deployment
# Version: 1.0.0
# ============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Script configuration
SCRIPT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/tmp/hauling-qr-deploy-$(date +%Y%m%d_%H%M%S).log"
CHECKPOINT_FILE="/tmp/hauling-qr-checkpoint"
APP_DIR="/opt/hauling-qr-system"
APP_USER="hauling"
DB_NAME="hauling_qr_system"
DB_USER="hauling_user"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Progress tracking
TOTAL_STEPS=11
CURRENT_STEP=0

# Configuration variables (will be set by interactive setup)
DOMAIN_NAME=""
DB_PASSWORD=""
JWT_SECRET=""
ADMIN_USERNAME="admin"
ADMIN_PASSWORD=""
ENABLE_SSL=false
SSL_EMAIL=""
BACKUP_SCHEDULE="daily"
LOG_LEVEL="info"
ENVIRONMENT="production"
REPO_URL=""

# Command line options
DRY_RUN=false
VERBOSE=false
QUIET=false
CONFIG_FILE=""
SKIP_SSL=false
RESUME_FROM=""
ROLLBACK_TO=""
CLEAN_INSTALL=false

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    if [[ "$QUIET" == false ]]; then
        case $level in
            "ERROR")   echo -e "${RED}❌ $message${NC}" ;;
            "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
            "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
            "INFO")    echo -e "${BLUE}ℹ️  $message${NC}" ;;
            "DEBUG")   [[ "$VERBOSE" == true ]] && echo -e "${PURPLE}🔍 $message${NC}" ;;
            "STEP")    echo -e "${CYAN}🔄 $message${NC}" ;;
            *)         echo "$message" ;;
        esac
    fi
}

# Progress indicator
show_progress() {
    local current=$1
    local total=$2
    local step_name=$3
    
    local percentage=$((current * 100 / total))
    local filled=$((current * 50 / total))
    local empty=$((50 - filled))
    
    printf "\r${CYAN}Progress: [%s%s] %d%% - %s${NC}" \
        "$(printf "%*s" $filled | tr ' ' '█')" \
        "$(printf "%*s" $empty | tr ' ' '░')" \
        $percentage \
        "$step_name"
    
    if [[ $current -eq $total ]]; then
        echo ""
    fi
}

# Checkpoint management
save_checkpoint() {
    local step=$1
    echo "$step" > "$CHECKPOINT_FILE"
    log "DEBUG" "Checkpoint saved: $step"
}

load_checkpoint() {
    if [[ -f "$CHECKPOINT_FILE" ]]; then
        cat "$CHECKPOINT_FILE"
    else
        echo "0"
    fi
}

# Error handling with rollback
handle_error() {
    local exit_code=$?
    local line_number=$1
    
    log "ERROR" "Script failed at line $line_number with exit code $exit_code"
    log "ERROR" "Last command: ${BASH_COMMAND}"
    
    if [[ "$DRY_RUN" == false ]]; then
        log "WARNING" "Initiating automatic rollback..."
        rollback_changes
    fi
    
    exit $exit_code
}

# Set error trap
trap 'handle_error $LINENO' ERR

# System requirements check
check_system_requirements() {
    log "STEP" "Checking system requirements..."
    
    # Check Ubuntu version
    if ! grep -q "Ubuntu 24.04" /etc/os-release; then
        log "WARNING" "This script is designed for Ubuntu 24.04. Current version:"
        cat /etc/os-release | grep PRETTY_NAME
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Check available disk space (minimum 20GB)
    local available_space=$(df / | awk 'NR==2 {print $4}')
    local required_space=$((20 * 1024 * 1024)) # 20GB in KB
    
    if [[ $available_space -lt $required_space ]]; then
        log "ERROR" "Insufficient disk space. Required: 20GB, Available: $((available_space / 1024 / 1024))GB"
        exit 1
    fi
    
    # Check available RAM (minimum 2GB)
    local available_ram=$(free -m | awk 'NR==2{print $2}')
    if [[ $available_ram -lt 2048 ]]; then
        log "WARNING" "Low RAM detected. Recommended: 2GB, Available: ${available_ram}MB"
    fi
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        log "ERROR" "This script should not be run as root for security reasons"
        exit 1
    fi
    
    # Check sudo access
    if ! sudo -n true 2>/dev/null; then
        log "ERROR" "This script requires sudo access"
        exit 1
    fi
    
    log "SUCCESS" "System requirements check passed"
}

# Generate secure random password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Validate domain name
validate_domain() {
    local domain=$1
    if [[ $domain =~ ^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$ ]]; then
        return 0
    else
        return 1
    fi
}

# Validate password strength
validate_password() {
    local password=$1
    local min_length=12
    
    if [[ ${#password} -lt $min_length ]]; then
        return 1
    fi
    
    # Check for mixed case, numbers, and symbols
    if [[ $password =~ [a-z] ]] && [[ $password =~ [A-Z] ]] && [[ $password =~ [0-9] ]] && [[ $password =~ [^a-zA-Z0-9] ]]; then
        return 0
    else
        return 1
    fi
}

# DNS validation
validate_dns() {
    local domain=$1
    log "INFO" "Validating DNS for $domain..."
    
    if nslookup "$domain" >/dev/null 2>&1; then
        local ip=$(nslookup "$domain" | awk '/^Address: / { print $2 }' | tail -1)
        local server_ip=$(curl -s ifconfig.me)
        
        if [[ "$ip" == "$server_ip" ]]; then
            log "SUCCESS" "DNS validation passed - $domain points to this server ($server_ip)"
            return 0
        else
            log "WARNING" "DNS mismatch - $domain points to $ip, but server IP is $server_ip"
            return 1
        fi
    else
        log "WARNING" "DNS lookup failed for $domain"
        return 1
    fi
}

# ============================================================================
# INTERACTIVE CONFIGURATION
# ============================================================================

interactive_setup() {
    log "STEP" "Starting interactive configuration wizard..."
    
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    HAULING QR TRIP SYSTEM DEPLOYMENT                        ║"
    echo "║                           Configuration Wizard                              ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    # Repository URL
    while [[ -z "$REPO_URL" ]]; do
        read -p "📦 Enter Git repository URL: " REPO_URL
        if [[ ! $REPO_URL =~ ^https?:// ]] && [[ ! $REPO_URL =~ ^git@ ]]; then
            log "ERROR" "Invalid repository URL format"
            REPO_URL=""
        fi
    done
    
    # Domain configuration
    while [[ -z "$DOMAIN_NAME" ]]; do
        read -p "🌐 Enter domain name (e.g., hauling.example.com): " DOMAIN_NAME
        if ! validate_domain "$DOMAIN_NAME"; then
            log "ERROR" "Invalid domain name format"
            DOMAIN_NAME=""
        else
            if ! validate_dns "$DOMAIN_NAME"; then
                read -p "DNS validation failed. Continue anyway? (y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    DOMAIN_NAME=""
                fi
            fi
        fi
    done
    
    # Database password
    while [[ -z "$DB_PASSWORD" ]]; do
        read -s -p "🔐 Enter database password (min 12 chars, mixed case, numbers, symbols): " DB_PASSWORD
        echo
        if ! validate_password "$DB_PASSWORD"; then
            log "ERROR" "Password does not meet security requirements"
            DB_PASSWORD=""
        fi
    done
    
    # JWT Secret
    read -p "🔑 Generate JWT secret automatically? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        while [[ ${#JWT_SECRET} -lt 64 ]]; do
            read -s -p "🔑 Enter JWT secret (min 64 characters): " JWT_SECRET
            echo
        done
    else
        JWT_SECRET=$(generate_password 64)
        log "SUCCESS" "JWT secret generated automatically"
    fi
    
    # Admin credentials
    read -p "👤 Admin username (default: admin): " input_admin
    ADMIN_USERNAME=${input_admin:-admin}
    
    while [[ -z "$ADMIN_PASSWORD" ]]; do
        read -s -p "🔐 Admin password: " ADMIN_PASSWORD
        echo
        if ! validate_password "$ADMIN_PASSWORD"; then
            log "ERROR" "Admin password does not meet security requirements"
            ADMIN_PASSWORD=""
        fi
    done
    
    # SSL Configuration
    read -p "🔒 Enable SSL with Let's Encrypt? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        ENABLE_SSL=true
        read -p "📧 Enter email for Let's Encrypt notifications: " SSL_EMAIL
        while [[ ! $SSL_EMAIL =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; do
            log "ERROR" "Invalid email format"
            read -p "📧 Enter valid email: " SSL_EMAIL
        done
    fi
    
    # Environment selection
    read -p "🏭 Environment (production/staging) [production]: " input_env
    ENVIRONMENT=${input_env:-production}
    
    # Backup configuration
    read -p "💾 Backup schedule (daily/weekly) [daily]: " input_backup
    BACKUP_SCHEDULE=${input_backup:-daily}
    
    # Log level
    read -p "📝 Log level (info/debug) [info]: " input_log
    LOG_LEVEL=${input_log:-info}
    
    # Configuration summary
    echo -e "\n${CYAN}Configuration Summary:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "Repository:     $REPO_URL"
    echo "Domain:         $DOMAIN_NAME"
    echo "Database User:  $DB_USER"
    echo "Admin User:     $ADMIN_USERNAME"
    echo "SSL Enabled:    $ENABLE_SSL"
    echo "Environment:    $ENVIRONMENT"
    echo "Backup:         $BACKUP_SCHEDULE"
    echo "Log Level:      $LOG_LEVEL"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    read -p "Proceed with deployment? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log "INFO" "Deployment cancelled by user"
        exit 0
    fi
    
    log "SUCCESS" "Interactive configuration completed"
}

# ============================================================================
# MAIN DEPLOYMENT FUNCTIONS
# ============================================================================

# Step 1: System preparation
step_system_preparation() {
    CURRENT_STEP=1
    show_progress $CURRENT_STEP $TOTAL_STEPS "System Preparation"
    log "STEP" "Step 1: System preparation and package updates"
    
    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would update system packages and install dependencies"
        return 0
    fi
    
    # Update package lists
    log "INFO" "Updating package lists..."
    sudo apt update -qq
    
    # Upgrade system packages
    log "INFO" "Upgrading system packages..."
    sudo DEBIAN_FRONTEND=noninteractive apt upgrade -y -qq
    
    # Install essential packages
    log "INFO" "Installing essential packages..."
    sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq \
        curl wget gnupg2 software-properties-common apt-transport-https \
        ca-certificates git build-essential unzip ufw fail2ban \
        htop tree jq openssl
    
    save_checkpoint "1"
    log "SUCCESS" "System preparation completed"
}

# Step 2: Node.js installation
step_nodejs_installation() {
    CURRENT_STEP=2
    show_progress $CURRENT_STEP $TOTAL_STEPS "Node.js Installation"
    log "STEP" "Step 2: Installing Node.js 18.x"
    
    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would install Node.js 18.x from NodeSource"
        return 0
    fi
    
    # Check if Node.js is already installed with correct version
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ "$node_version" == "18" ]]; then
            log "INFO" "Node.js 18.x already installed"
            save_checkpoint "2"
            return 0
        fi
    fi
    
    # Add NodeSource repository
    log "INFO" "Adding NodeSource repository..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    
    # Install Node.js
    log "INFO" "Installing Node.js..."
    sudo DEBIAN_FRONTEND=noninteractive apt install -y nodejs
    
    # Verify installation
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    log "SUCCESS" "Node.js installed: $node_version, npm: $npm_version"
    
    # Install PM2 globally
    log "INFO" "Installing PM2 process manager..."
    sudo npm install -g pm2
    
    save_checkpoint "2"
    log "SUCCESS" "Node.js installation completed"
}

# Step 3: PostgreSQL installation and configuration
step_postgresql_installation() {
    CURRENT_STEP=3
    show_progress $CURRENT_STEP $TOTAL_STEPS "PostgreSQL Installation"
    log "STEP" "Step 3: Installing and configuring PostgreSQL 15"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would install PostgreSQL 15 and configure database"
        return 0
    fi

    # Install PostgreSQL
    log "INFO" "Installing PostgreSQL..."
    sudo DEBIAN_FRONTEND=noninteractive apt install -y postgresql postgresql-contrib

    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql

    # Configure PostgreSQL
    log "INFO" "Configuring PostgreSQL..."

    # Create database and user
    sudo -u postgres psql -c "CREATE DATABASE $DB_NAME;" 2>/dev/null || true
    sudo -u postgres psql -c "CREATE USER $DB_USER WITH ENCRYPTED PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
    sudo -u postgres psql -c "ALTER USER $DB_USER CREATEDB;"

    # Configure PostgreSQL for production
    local pg_version="15"
    local pg_config="/etc/postgresql/$pg_version/main/postgresql.conf"
    local pg_hba="/etc/postgresql/$pg_version/main/pg_hba.conf"

    # Update postgresql.conf
    sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" "$pg_config"
    sudo sed -i "s/#max_connections = 100/max_connections = 200/" "$pg_config"
    sudo sed -i "s/#shared_buffers = 128MB/shared_buffers = 256MB/" "$pg_config"

    # Update pg_hba.conf for local connections
    if ! sudo grep -q "local   $DB_NAME   $DB_USER" "$pg_hba"; then
        echo "local   $DB_NAME   $DB_USER                     md5" | sudo tee -a "$pg_hba"
    fi

    # Restart PostgreSQL
    sudo systemctl restart postgresql

    # Test database connection
    if PGPASSWORD="$DB_PASSWORD" psql -h localhost -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
        log "SUCCESS" "Database connection test passed"
    else
        log "ERROR" "Database connection test failed"
        exit 1
    fi

    save_checkpoint "3"
    log "SUCCESS" "PostgreSQL installation completed"
}

# Step 4: Application user and directory setup
step_application_setup() {
    CURRENT_STEP=4
    show_progress $CURRENT_STEP $TOTAL_STEPS "Application Setup"
    log "STEP" "Step 4: Setting up application user and directories"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would create application user and clone repository"
        return 0
    fi

    # Create application user
    if ! id "$APP_USER" >/dev/null 2>&1; then
        log "INFO" "Creating application user: $APP_USER"
        sudo useradd -r -s /bin/bash -d "$APP_DIR" -m "$APP_USER"
    fi

    # Create application directory
    sudo mkdir -p "$APP_DIR"
    sudo chown "$APP_USER:$APP_USER" "$APP_DIR"

    # Clone repository
    log "INFO" "Cloning repository..."
    if [[ -d "$APP_DIR/.git" ]]; then
        log "INFO" "Repository already exists, pulling latest changes..."
        sudo -u "$APP_USER" git -C "$APP_DIR" pull origin main
    else
        sudo -u "$APP_USER" git clone "$REPO_URL" "$APP_DIR"
    fi

    # Set proper permissions
    sudo chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    sudo chmod -R 755 "$APP_DIR"

    # Create logs directory
    sudo -u "$APP_USER" mkdir -p "$APP_DIR/logs"

    save_checkpoint "4"
    log "SUCCESS" "Application setup completed"
}

# Step 5: Environment configuration
step_environment_configuration() {
    CURRENT_STEP=5
    show_progress $CURRENT_STEP $TOTAL_STEPS "Environment Configuration"
    log "STEP" "Step 5: Configuring environment variables"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would create production .env file"
        return 0
    fi

    # Generate production .env file
    local env_file="$APP_DIR/.env"

    log "INFO" "Creating production environment configuration..."

    cat > "/tmp/hauling-qr.env" << EOF
# ============================================================================
# HAULING QR TRIP SYSTEM - PRODUCTION CONFIGURATION
# Generated on: $(date)
# ============================================================================

# Environment
NODE_ENV=$ENVIRONMENT

# Server Configuration
PORT=5000
HOST=0.0.0.0
BACKEND_HTTP_PORT=5000
BACKEND_HTTPS_PORT=5444

# IP Detection
AUTO_DETECT_IP=true
LOCAL_NETWORK_IP=$(hostname -I | awk '{print $1}')

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD
DB_POOL_MAX=25
DB_POOL_MIN=5

# Security Configuration
JWT_SECRET=$JWT_SECRET
JWT_EXPIRY=24h

# CORS Configuration
CORS_ORIGIN=https://$DOMAIN_NAME
ALLOWED_ORIGINS=https://$DOMAIN_NAME,https://www.$DOMAIN_NAME

# SSL/HTTPS Configuration
ENABLE_HTTPS=$ENABLE_SSL
SSL_KEY_PATH=$APP_DIR/ssl/private.key
SSL_CERT_PATH=$APP_DIR/ssl/certificate.crt

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=$LOG_LEVEL
LOG_FILE_ENABLED=true
LOG_FILE_PATH=$APP_DIR/logs/app.log

# Client Configuration
REACT_APP_API_URL=https://$DOMAIN_NAME/api
REACT_APP_WS_URL=wss://$DOMAIN_NAME
REACT_APP_USE_HTTPS=$ENABLE_SSL
REACT_APP_LOCAL_NETWORK_IP=$(hostname -I | awk '{print $1}')

# Performance Configuration
CLUSTER_MODE=true
MAX_WORKERS=4
EOF

    # Move env file to application directory
    sudo mv "/tmp/hauling-qr.env" "$env_file"
    sudo chown "$APP_USER:$APP_USER" "$env_file"
    sudo chmod 600 "$env_file"

    save_checkpoint "5"
    log "SUCCESS" "Environment configuration completed"
}

# Step 6: Dependencies installation
step_dependencies_installation() {
    CURRENT_STEP=6
    show_progress $CURRENT_STEP $TOTAL_STEPS "Dependencies Installation"
    log "STEP" "Step 6: Installing application dependencies"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would install npm dependencies"
        return 0
    fi

    # Install root dependencies
    log "INFO" "Installing root dependencies..."
    sudo -u "$APP_USER" bash -c "cd '$APP_DIR' && npm install --production"

    # Install server dependencies
    log "INFO" "Installing server dependencies..."
    sudo -u "$APP_USER" bash -c "cd '$APP_DIR/server' && npm install --production"

    # Install client dependencies
    log "INFO" "Installing client dependencies..."
    sudo -u "$APP_USER" bash -c "cd '$APP_DIR/client' && npm install"

    save_checkpoint "6"
    log "SUCCESS" "Dependencies installation completed"
}

# Step 7: Database initialization
step_database_initialization() {
    CURRENT_STEP=7
    show_progress $CURRENT_STEP $TOTAL_STEPS "Database Initialization"
    log "STEP" "Step 7: Initializing database schema"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would initialize database with init.sql"
        return 0
    fi

    # Initialize database with init.sql
    log "INFO" "Executing database initialization script..."

    if PGPASSWORD="$DB_PASSWORD" psql -h localhost -U "$DB_USER" -d "$DB_NAME" -f "$APP_DIR/database/init.sql"; then
        log "SUCCESS" "Database schema initialized successfully"
    else
        log "ERROR" "Database initialization failed"
        exit 1
    fi

    # Verify database structure
    local table_count=$(PGPASSWORD="$DB_PASSWORD" psql -h localhost -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')

    if [[ $table_count -gt 0 ]]; then
        log "SUCCESS" "Database verification passed: $table_count tables created"
    else
        log "ERROR" "Database verification failed: no tables found"
        exit 1
    fi

    # Create admin user
    log "INFO" "Creating admin user..."
    local password_hash=$(node -e "const bcrypt = require('bcryptjs'); console.log(bcrypt.hashSync('$ADMIN_PASSWORD', 10));")

    PGPASSWORD="$DB_PASSWORD" psql -h localhost -U "$DB_USER" -d "$DB_NAME" -c "
        INSERT INTO users (username, email, password_hash, full_name, role, created_at, updated_at)
        VALUES ('$ADMIN_USERNAME', 'admin@$DOMAIN_NAME', '$password_hash', 'System Administrator', 'admin', NOW(), NOW())
        ON CONFLICT (username) DO UPDATE SET
            password_hash = EXCLUDED.password_hash,
            updated_at = NOW();
    " 2>/dev/null || true

    save_checkpoint "7"
    log "SUCCESS" "Database initialization completed"
}

# Step 8: Frontend build
step_frontend_build() {
    CURRENT_STEP=8
    show_progress $CURRENT_STEP $TOTAL_STEPS "Frontend Build"
    log "STEP" "Step 8: Building React frontend for production"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would build React application"
        return 0
    fi

    # Build React application
    log "INFO" "Building React application..."
    sudo -u "$APP_USER" bash -c "cd '$APP_DIR/client' && npm run build"

    # Verify build was created
    if [[ -d "$APP_DIR/client/build" ]]; then
        local build_size=$(du -sh "$APP_DIR/client/build" | cut -f1)
        log "SUCCESS" "Frontend build completed successfully (Size: $build_size)"
    else
        log "ERROR" "Frontend build failed - build directory not found"
        exit 1
    fi

    save_checkpoint "8"
    log "SUCCESS" "Frontend build completed"
}

# Step 9: Nginx installation and configuration
step_nginx_configuration() {
    CURRENT_STEP=9
    show_progress $CURRENT_STEP $TOTAL_STEPS "Nginx Configuration"
    log "STEP" "Step 9: Installing and configuring Nginx"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would install and configure Nginx"
        return 0
    fi

    # Install Nginx
    log "INFO" "Installing Nginx..."
    sudo DEBIAN_FRONTEND=noninteractive apt install -y nginx

    # Create Nginx configuration
    log "INFO" "Creating Nginx configuration..."

    cat > "/tmp/hauling-qr-nginx.conf" << EOF
server {
    listen 80;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    # Serve React build files
    location / {
        root $APP_DIR/client/build;
        index index.html index.htm;
        try_files \$uri \$uri/ /index.html;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Proxy API requests to Node.js backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://localhost:5000/health;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

    # Install Nginx configuration
    sudo mv "/tmp/hauling-qr-nginx.conf" "/etc/nginx/sites-available/hauling-qr-system"
    sudo ln -sf "/etc/nginx/sites-available/hauling-qr-system" "/etc/nginx/sites-enabled/"

    # Remove default site
    sudo rm -f "/etc/nginx/sites-enabled/default"

    # Test Nginx configuration
    if sudo nginx -t; then
        log "SUCCESS" "Nginx configuration test passed"
    else
        log "ERROR" "Nginx configuration test failed"
        exit 1
    fi

    # Start and enable Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx

    save_checkpoint "9"
    log "SUCCESS" "Nginx configuration completed"
}

# Step 10: Process management with PM2
step_process_management() {
    CURRENT_STEP=10
    show_progress $CURRENT_STEP $TOTAL_STEPS "Process Management"
    log "STEP" "Step 10: Configuring PM2 process management"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would configure PM2 process management"
        return 0
    fi

    # Create PM2 ecosystem file
    log "INFO" "Creating PM2 ecosystem configuration..."

    cat > "$APP_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [{
    name: 'hauling-qr-system',
    script: 'server/server.js',
    cwd: '$APP_DIR',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: '$ENVIRONMENT',
      PORT: 5000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '$APP_DIR/logs/err.log',
    out_file: '$APP_DIR/logs/out.log',
    log_file: '$APP_DIR/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'client/build'],
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF

    sudo chown "$APP_USER:$APP_USER" "$APP_DIR/ecosystem.config.js"

    # Start application with PM2
    log "INFO" "Starting application with PM2..."
    sudo -u "$APP_USER" bash -c "cd '$APP_DIR' && pm2 start ecosystem.config.js --env $ENVIRONMENT"

    # Save PM2 configuration
    sudo -u "$APP_USER" pm2 save

    # Setup PM2 startup script
    local startup_cmd=$(sudo -u "$APP_USER" pm2 startup | grep "sudo env" | head -1)
    if [[ -n "$startup_cmd" ]]; then
        eval "$startup_cmd"
        log "SUCCESS" "PM2 startup script configured"
    fi

    save_checkpoint "10"
    log "SUCCESS" "Process management configuration completed"
}

# Step 11: Security and SSL configuration
step_security_configuration() {
    CURRENT_STEP=11
    show_progress $CURRENT_STEP $TOTAL_STEPS "Security Configuration"
    log "STEP" "Step 11: Configuring security and SSL"

    if [[ "$DRY_RUN" == true ]]; then
        log "INFO" "[DRY RUN] Would configure firewall and SSL"
        return 0
    fi

    # Configure UFW firewall
    log "INFO" "Configuring firewall..."
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing

    # Allow SSH (current connection)
    sudo ufw allow ssh

    # Allow HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp

    # Enable firewall
    sudo ufw --force enable

    # Configure fail2ban
    log "INFO" "Configuring fail2ban..."
    sudo systemctl start fail2ban
    sudo systemctl enable fail2ban

    # SSL Configuration
    if [[ "$ENABLE_SSL" == true ]] && [[ "$SKIP_SSL" == false ]]; then
        log "INFO" "Setting up SSL with Let's Encrypt..."

        # Install Certbot
        sudo DEBIAN_FRONTEND=noninteractive apt install -y certbot python3-certbot-nginx

        # Obtain SSL certificate
        if sudo certbot --nginx -d "$DOMAIN_NAME" -d "www.$DOMAIN_NAME" --non-interactive --agree-tos --email "$SSL_EMAIL"; then
            log "SUCCESS" "SSL certificate obtained successfully"

            # Test automatic renewal
            sudo certbot renew --dry-run
            log "SUCCESS" "SSL automatic renewal test passed"
        else
            log "WARNING" "SSL certificate setup failed, continuing with HTTP"
        fi
    fi

    save_checkpoint "11"
    log "SUCCESS" "Security configuration completed"
}

# ============================================================================
# VERIFICATION AND TESTING
# ============================================================================

# Comprehensive system verification
verify_deployment() {
    log "STEP" "Verifying deployment..."

    local verification_failed=false

    # Check PostgreSQL
    log "INFO" "Checking PostgreSQL service..."
    if sudo systemctl is-active --quiet postgresql; then
        log "SUCCESS" "PostgreSQL is running"
    else
        log "ERROR" "PostgreSQL is not running"
        verification_failed=true
    fi

    # Check database connection
    log "INFO" "Testing database connection..."
    if PGPASSWORD="$DB_PASSWORD" psql -h localhost -U "$DB_USER" -d "$DB_NAME" -c "SELECT COUNT(*) FROM users;" >/dev/null 2>&1; then
        log "SUCCESS" "Database connection successful"
    else
        log "ERROR" "Database connection failed"
        verification_failed=true
    fi

    # Check Nginx
    log "INFO" "Checking Nginx service..."
    if sudo systemctl is-active --quiet nginx; then
        log "SUCCESS" "Nginx is running"
    else
        log "ERROR" "Nginx is not running"
        verification_failed=true
    fi

    # Check PM2 application
    log "INFO" "Checking PM2 application..."
    if sudo -u "$APP_USER" pm2 list | grep -q "hauling-qr-system.*online"; then
        log "SUCCESS" "PM2 application is running"
    else
        log "ERROR" "PM2 application is not running"
        verification_failed=true
    fi

    # Test API endpoint
    log "INFO" "Testing API health endpoint..."
    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if curl -s "http://localhost/health" | grep -q "OK"; then
            log "SUCCESS" "API health check passed"
            break
        else
            if [[ $attempt -eq $max_attempts ]]; then
                log "ERROR" "API health check failed after $max_attempts attempts"
                verification_failed=true
            else
                log "INFO" "API not ready, waiting... (attempt $attempt/$max_attempts)"
                sleep 2
            fi
        fi
        ((attempt++))
    done

    # Test frontend
    log "INFO" "Testing frontend accessibility..."
    if curl -s "http://localhost/" | grep -q "<!doctype html>"; then
        log "SUCCESS" "Frontend is accessible"
    else
        log "ERROR" "Frontend is not accessible"
        verification_failed=true
    fi

    # Test SSL if enabled
    if [[ "$ENABLE_SSL" == true ]] && [[ "$SKIP_SSL" == false ]]; then
        log "INFO" "Testing SSL certificate..."
        if curl -s "https://$DOMAIN_NAME/health" | grep -q "OK"; then
            log "SUCCESS" "SSL is working correctly"
        else
            log "WARNING" "SSL test failed, but deployment may still be functional"
        fi
    fi

    if [[ "$verification_failed" == true ]]; then
        log "ERROR" "Deployment verification failed"
        return 1
    else
        log "SUCCESS" "All verification tests passed"
        return 0
    fi
}

# ============================================================================
# ROLLBACK AND RECOVERY
# ============================================================================

# Rollback changes
rollback_changes() {
    log "WARNING" "Initiating rollback procedure..."

    local checkpoint=$(load_checkpoint)

    case $checkpoint in
        "11"|"10"|"9"|"8"|"7"|"6"|"5"|"4")
            log "INFO" "Stopping PM2 processes..."
            sudo -u "$APP_USER" pm2 delete all 2>/dev/null || true

            log "INFO" "Stopping Nginx..."
            sudo systemctl stop nginx 2>/dev/null || true

            log "INFO" "Removing application directory..."
            sudo rm -rf "$APP_DIR" 2>/dev/null || true

            log "INFO" "Removing application user..."
            sudo userdel -r "$APP_USER" 2>/dev/null || true
            ;&
        "3")
            log "INFO" "Dropping database..."
            sudo -u postgres psql -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null || true
            sudo -u postgres psql -c "DROP USER IF EXISTS $DB_USER;" 2>/dev/null || true
            ;&
        "2")
            log "INFO" "Removing Node.js (manual removal required)..."
            ;&
        "1")
            log "INFO" "System packages will remain installed for safety"
            ;;
        *)
            log "INFO" "No rollback needed - no changes were made"
            ;;
    esac

    # Remove checkpoint file
    rm -f "$CHECKPOINT_FILE"

    log "SUCCESS" "Rollback completed"
}

# ============================================================================
# MAINTENANCE SCRIPTS GENERATION
# ============================================================================

# Generate maintenance scripts
generate_maintenance_scripts() {
    log "INFO" "Generating maintenance scripts..."

    local scripts_dir="$APP_DIR/scripts"
    sudo -u "$APP_USER" mkdir -p "$scripts_dir"

    # Backup script
    cat > "$scripts_dir/backup.sh" << 'EOF'
#!/bin/bash
# Automated backup script for Hauling QR Trip System

BACKUP_DIR="/opt/hauling-qr-system/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="hauling_qr_system"
DB_USER="hauling_user"

mkdir -p "$BACKUP_DIR"

# Database backup
echo "Creating database backup..."
pg_dump -h localhost -U "$DB_USER" "$DB_NAME" > "$BACKUP_DIR/db_backup_$DATE.sql"

# Application backup
echo "Creating application backup..."
tar -czf "$BACKUP_DIR/app_backup_$DATE.tar.gz" -C /opt hauling-qr-system --exclude=node_modules --exclude=backups --exclude=logs

# Cleanup old backups (keep last 7 days)
find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF

    # Health check script
    cat > "$scripts_dir/health-check.sh" << 'EOF'
#!/bin/bash
# Health check script for Hauling QR Trip System

echo "=== Hauling QR Trip System Health Check ==="
echo "Date: $(date)"
echo

# Check services
echo "Service Status:"
echo "- PostgreSQL: $(systemctl is-active postgresql)"
echo "- Nginx: $(systemctl is-active nginx)"
echo "- PM2 App: $(sudo -u hauling pm2 list | grep hauling-qr-system | awk '{print $10}')"
echo

# Check disk space
echo "Disk Usage:"
df -h / | tail -1
echo

# Check memory usage
echo "Memory Usage:"
free -h
echo

# Check API health
echo "API Health:"
curl -s http://localhost/health | jq . 2>/dev/null || echo "API not responding"
echo

# Check logs for errors
echo "Recent Errors (last 10):"
tail -10 /opt/hauling-qr-system/logs/err.log 2>/dev/null || echo "No error logs found"
EOF

    # Update script
    cat > "$scripts_dir/update.sh" << 'EOF'
#!/bin/bash
# Update script for Hauling QR Trip System

APP_DIR="/opt/hauling-qr-system"
APP_USER="hauling"

echo "Starting update process..."

# Backup before update
./backup.sh

# Pull latest changes
echo "Pulling latest changes..."
sudo -u "$APP_USER" git -C "$APP_DIR" pull origin main

# Install dependencies
echo "Installing dependencies..."
sudo -u "$APP_USER" bash -c "cd '$APP_DIR' && npm install --production"
sudo -u "$APP_USER" bash -c "cd '$APP_DIR/server' && npm install --production"
sudo -u "$APP_USER" bash -c "cd '$APP_DIR/client' && npm install"

# Build frontend
echo "Building frontend..."
sudo -u "$APP_USER" bash -c "cd '$APP_DIR/client' && npm run build"

# Restart application
echo "Restarting application..."
sudo -u "$APP_USER" pm2 restart hauling-qr-system

echo "Update completed successfully"
EOF

    # Make scripts executable
    sudo chmod +x "$scripts_dir"/*.sh
    sudo chown -R "$APP_USER:$APP_USER" "$scripts_dir"

    # Setup cron job for backups
    if [[ "$BACKUP_SCHEDULE" == "daily" ]]; then
        echo "0 2 * * * $scripts_dir/backup.sh" | sudo -u "$APP_USER" crontab -
    elif [[ "$BACKUP_SCHEDULE" == "weekly" ]]; then
        echo "0 2 * * 0 $scripts_dir/backup.sh" | sudo -u "$APP_USER" crontab -
    fi

    log "SUCCESS" "Maintenance scripts generated"
}

# ============================================================================
# MAIN DEPLOYMENT ORCHESTRATION
# ============================================================================

# Main deployment function
main_deployment() {
    log "INFO" "Starting Hauling QR Trip System deployment..."

    # Load checkpoint if resuming
    local start_step=1
    if [[ -n "$RESUME_FROM" ]]; then
        start_step=$RESUME_FROM
        log "INFO" "Resuming deployment from step $start_step"
    elif [[ -f "$CHECKPOINT_FILE" ]]; then
        start_step=$(($(load_checkpoint) + 1))
        if [[ $start_step -le $TOTAL_STEPS ]]; then
            log "INFO" "Resuming deployment from step $start_step"
        fi
    fi

    # Execute deployment steps
    if [[ $start_step -le 1 ]]; then step_system_preparation; fi
    if [[ $start_step -le 2 ]]; then step_nodejs_installation; fi
    if [[ $start_step -le 3 ]]; then step_postgresql_installation; fi
    if [[ $start_step -le 4 ]]; then step_application_setup; fi
    if [[ $start_step -le 5 ]]; then step_environment_configuration; fi
    if [[ $start_step -le 6 ]]; then step_dependencies_installation; fi
    if [[ $start_step -le 7 ]]; then step_database_initialization; fi
    if [[ $start_step -le 8 ]]; then step_frontend_build; fi
    if [[ $start_step -le 9 ]]; then step_nginx_configuration; fi
    if [[ $start_step -le 10 ]]; then step_process_management; fi
    if [[ $start_step -le 11 ]]; then step_security_configuration; fi

    # Generate maintenance scripts
    generate_maintenance_scripts

    # Verify deployment
    if verify_deployment; then
        log "SUCCESS" "Deployment completed successfully!"
        show_deployment_summary
    else
        log "ERROR" "Deployment verification failed"
        exit 1
    fi
}

# Show deployment summary
show_deployment_summary() {
    echo -e "\n${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    DEPLOYMENT COMPLETED SUCCESSFULLY!                       ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo -e "${CYAN}🎉 Hauling QR Trip System is now running!${NC}\n"

    echo "📋 System Information:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "🌐 Application URL:    http://$DOMAIN_NAME"
    if [[ "$ENABLE_SSL" == true ]]; then
        echo "🔒 Secure URL:         https://$DOMAIN_NAME"
    fi
    echo "🏥 Health Check:       http://$DOMAIN_NAME/health"
    echo "📊 API Endpoint:       http://$DOMAIN_NAME/api"
    echo "👤 Admin Username:     $ADMIN_USERNAME"
    echo "📁 Application Path:   $APP_DIR"
    echo "📝 Log Files:          $APP_DIR/logs/"
    echo "🔧 Maintenance Scripts: $APP_DIR/scripts/"
    echo

    echo "🛠️ Management Commands:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "View application status:  sudo -u $APP_USER pm2 status"
    echo "View application logs:    sudo -u $APP_USER pm2 logs hauling-qr-system"
    echo "Restart application:      sudo -u $APP_USER pm2 restart hauling-qr-system"
    echo "Run health check:         $APP_DIR/scripts/health-check.sh"
    echo "Create backup:            $APP_DIR/scripts/backup.sh"
    echo "Update application:       $APP_DIR/scripts/update.sh"
    echo

    echo "🔐 Security Information:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "Firewall status:          $(sudo ufw status | head -1)"
    echo "SSL enabled:              $ENABLE_SSL"
    echo "Backup schedule:          $BACKUP_SCHEDULE"
    echo "Environment:              $ENVIRONMENT"
    echo

    echo "📚 Important Notes:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "• Change default admin password after first login"
    echo "• Monitor system resources and logs regularly"
    echo "• Keep the system updated with security patches"
    echo "• Backup database regularly (automated: $BACKUP_SCHEDULE)"
    echo "• SSL certificates will auto-renew via certbot"
    echo

    echo "📞 Support:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "Deployment log:           $LOG_FILE"
    echo "Troubleshooting guide:    $APP_DIR/scripts/health-check.sh"
    echo

    # Clean up checkpoint file on successful deployment
    rm -f "$CHECKPOINT_FILE"
}

# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

# Show help
show_help() {
    echo "Hauling QR Trip System - Automated Deployment Script v$SCRIPT_VERSION"
    echo
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -q, --quiet             Minimal output"
    echo "  --dry-run               Show what would be done without making changes"
    echo "  --config-file FILE      Use pre-configured answers file"
    echo "  --skip-ssl              Skip SSL certificate setup"
    echo "  --resume-from STEP      Resume deployment from specific step (1-11)"
    echo "  --rollback-to STEP      Rollback to specific checkpoint"
    echo "  --clean-install         Remove existing installation and start fresh"
    echo
    echo "Examples:"
    echo "  $0                      Interactive deployment"
    echo "  $0 --dry-run            Preview deployment without changes"
    echo "  $0 --verbose            Detailed deployment output"
    echo "  $0 --resume-from 5      Resume from step 5"
    echo "  $0 --rollback-to 3      Rollback to checkpoint 3"
    echo
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -q|--quiet)
                QUIET=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --config-file)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --skip-ssl)
                SKIP_SSL=true
                shift
                ;;
            --resume-from)
                RESUME_FROM="$2"
                shift 2
                ;;
            --rollback-to)
                ROLLBACK_TO="$2"
                shift 2
                ;;
            --clean-install)
                CLEAN_INSTALL=true
                shift
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# ============================================================================
# MAIN SCRIPT EXECUTION
# ============================================================================

main() {
    # Parse command line arguments
    parse_arguments "$@"

    # Initialize logging
    log "INFO" "Hauling QR Trip System Deployment Script v$SCRIPT_VERSION"
    log "INFO" "Log file: $LOG_FILE"

    # Handle special operations
    if [[ -n "$ROLLBACK_TO" ]]; then
        log "WARNING" "Initiating rollback to checkpoint $ROLLBACK_TO"
        rollback_changes
        exit 0
    fi

    if [[ "$CLEAN_INSTALL" == true ]]; then
        log "WARNING" "Clean install requested - removing existing installation"
        rollback_changes
    fi

    # Check system requirements
    check_system_requirements

    # Load configuration
    if [[ -n "$CONFIG_FILE" ]] && [[ -f "$CONFIG_FILE" ]]; then
        log "INFO" "Loading configuration from $CONFIG_FILE"
        source "$CONFIG_FILE"
    else
        interactive_setup
    fi

    # Start deployment
    main_deployment
}

# Execute main function with all arguments
main "$@"
