-- Test the evaluate_shift_status function on the problematic shift (ID 536)
SELECT 
    id,
    truck_id,
    driver_id,
    shift_type,
    status AS current_status,
    start_date,
    end_date,
    start_time,
    end_time,
    evaluate_shift_status(id) AS evaluated_status,
    CURRENT_DATE AS current_date,
    CURRENT_TIME AS current_time,
    CURRENT_TIMESTAMP AS current_timestamp,
    -- Calculate the end datetime for the shift
    CASE 
        WHEN end_time < start_time THEN -- overnight shift
            (end_date + INTERVAL '1 day')::DATE + end_time
        ELSE -- day shift
            end_date::DATE + end_time
    END AS shift_end_datetime,
    -- Check if current timestamp is past the end datetime
    CURRENT_TIMESTAMP > 
        CASE 
            WHEN end_time < start_time THEN -- overnight shift
                (end_date + INTERVAL '1 day')::DATE + end_time
            ELSE -- day shift
                end_date::DATE + end_time
        END AS is_past_end_datetime
FROM 
    driver_shifts
WHERE 
    id = 536;

-- Test with a specific reference timestamp to debug the logic
SELECT 
    evaluate_shift_status(536, '2025-07-15 17:30:00'::TIMESTAMP) AS with_current_time,
    evaluate_shift_status(536, '2025-07-31 06:01:00'::TIMESTAMP) AS with_end_date_past_end_time,
    evaluate_shift_status(536, '2025-07-31 05:59:00'::TIMESTAMP) AS with_end_date_before_end_time,
    evaluate_shift_status(536, '2025-08-01 00:00:00'::TIMESTAMP) AS with_past_end_date;