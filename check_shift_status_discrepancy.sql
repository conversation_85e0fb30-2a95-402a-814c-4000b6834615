-- Check the actual status of the shift in the database
SELECT 
    id,
    truck_id,
    driver_id,
    shift_type,
    status AS current_status,
    start_date,
    end_date,
    start_time,
    end_time,
    evaluate_shift_status(id) AS evaluated_status,
    (SELECT evaluated_status FROM debug_shift_status(id)) AS debug_status
FROM 
    driver_shifts
WHERE 
    id = 536;

-- Check when the shift was last updated
SELECT 
    id,
    status,
    created_at,
    updated_at
FROM 
    driver_shifts
WHERE 
    id = 536;

-- Check if there's a manual override or other process updating the status
SELECT 
    pg_get_functiondef(oid) 
FROM 
    pg_proc 
WHERE 
    proname LIKE '%shift%status%' 
    AND proname NOT IN ('evaluate_shift_status', 'update_shift_status', 'update_all_shift_statuses', 'debug_shift_status');