-- Debug the overnight shift logic in evaluate_shift_status function
-- Create a temporary function to expose the internal variables
CREATE OR REPLACE FUNCTION debug_shift_status(p_shift_id INTEGER, p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
RETURNS TABLE(
    shift_id INTEGER,
    start_date_val DATE,
    end_date_val DATE,
    start_time_val TIME,
    end_time_val TIME,
    current_date_val DATE,
    current_time_val TIME,
    is_overnight BOOLEAN,
    is_within_date_range BOOLEAN,
    is_within_time_window BOOLEAN,
    shift_end_datetime TIMESTAMP,
    is_past_completion BOOLEAN,
    evaluated_status TEXT
) AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_is_past_completion BOOLEAN;
    v_new_status TEXT;
    v_shift_end_datetime TIMESTAMP;
BEGIN
    -- Get shift details
    SELECT
        id,
        start_date,
        end_date,
        start_time,
        end_time,
        shift_type,
        status,
        recurrence_pattern
    INTO v_shift
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- Extract current date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;

    -- Check if shift spans overnight (night shift logic)
    v_is_overnight := v_shift.end_time < v_shift.start_time;

    -- Enhanced date range validation with unified approach
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;

    -- Enhanced time window validation with proper overnight logic
    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic for overnight spans
        v_is_within_time_window := (v_current_time >= v_shift.start_time OR v_current_time <= v_shift.end_time);
    ELSE
        -- Day shift: Use simple BETWEEN logic for same-day shifts
        v_is_within_time_window := (v_current_time BETWEEN v_shift.start_time AND v_shift.end_time);
    END IF;

    -- Enhanced completion logic with proper overnight handling
    IF v_is_overnight THEN
        -- For overnight shifts: completed when we're past end_time on the next day
        v_shift_end_datetime := (v_shift.end_date + INTERVAL '1 day')::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    ELSE
        -- For day shifts: completed when past end_time on the same day
        v_shift_end_datetime := v_shift.end_date::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    END IF;

    -- Apply status rules in priority order
    IF v_is_past_completion THEN
        -- Rule 3: Completed - past the shift's end datetime
        v_new_status := 'completed';
    ELSIF v_is_within_date_range AND v_is_within_time_window THEN
        -- Rule 1: Active - within date range AND within time window
        v_new_status := 'active';
    ELSIF v_is_within_date_range AND NOT v_is_within_time_window THEN
        -- Rule 2: Scheduled - within date range BUT outside time window
        v_new_status := 'scheduled';
    ELSE
        -- Default: Scheduled for future dates
        v_new_status := 'scheduled';
    END IF;

    -- Return all debug information
    RETURN QUERY SELECT 
        v_shift.id,
        v_shift.start_date,
        v_shift.end_date,
        v_shift.start_time,
        v_shift.end_time,
        v_current_date,
        v_current_time,
        v_is_overnight,
        v_is_within_date_range,
        v_is_within_time_window,
        v_shift_end_datetime,
        v_is_past_completion,
        v_new_status;
END;
$$ LANGUAGE plpgsql;