-- Check if there's a trigger or other process that might have set the status to completed
SELECT 
    tgname AS trigger_name,
    tgrelid::regclass AS table_name,
    pg_get_triggerdef(oid) AS trigger_definition
FROM 
    pg_trigger
WHERE 
    tgrelid = 'driver_shifts'::regclass;

-- Let's check if there's a background job or scheduled task that might be updating the status
SELECT 
    proname AS function_name,
    pg_get_functiondef(oid) AS function_definition
FROM 
    pg_proc
WHERE 
    proname LIKE '%job%' OR proname LIKE '%schedule%' OR proname LIKE '%cron%';

-- Let's check if there's a server-side function that might be updating the status
SELECT 
    proname AS function_name
FROM 
    pg_proc
WHERE 
    proname LIKE '%shift%' OR proname LIKE '%driver%' OR proname LIKE '%status%'
ORDER BY 
    proname;