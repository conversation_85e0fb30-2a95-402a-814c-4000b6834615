/**
 * Enhanced Shift Status Migration Runner
 * Purpose: Apply enhanced shift status evaluation functions
 * Usage: node database/run-enhanced-migration.js
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// Database configuration from .env
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function runMigration() {
  console.log('🚀 Running Enhanced Shift Status Migration...\n');

  const client = await pool.connect();
  
  try {
    // Read the enhanced migration file
    const migrationPath = path.join(__dirname, 'migrations', '036_create_shift_status_evaluation.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration file loaded successfully');

    // Check if migration has already been applied
    const checkResult = await client.query(`
      SELECT 1 FROM information_schema.routines 
      WHERE routine_name = 'evaluate_shift_status' 
      AND routine_type = 'FUNCTION'
    `);

    if (checkResult.rows.length > 0) {
      console.log('✅ Enhanced functions already exist, updating...');
    } else {
      console.log('📦 Installing enhanced functions for the first time...');
    }

    // Execute the migration
    console.log('⚙️ Executing migration...');
    await client.query(migrationSQL);
    console.log('✅ Migration executed successfully');

    // Verify the functions were created
    const verifyResult = await client.query(`
      SELECT routine_name, routine_type 
      FROM information_schema.routines 
      WHERE routine_name IN (
        'evaluate_shift_status',
        'update_all_shift_statuses',
        'update_shift_status',
        'get_shift_status_summary',
        'test_shift_time_logic'
      )
      ORDER BY routine_name
    `);

    console.log('\n📋 Verification Results:');
    if (verifyResult.rows.length >= 5) {
      console.log('✅ All enhanced functions created successfully:');
      verifyResult.rows.forEach(row => {
        console.log(`   • ${row.routine_name} (${row.routine_type})`);
      });
    } else {
      console.log('⚠️ Some functions may be missing:');
      verifyResult.rows.forEach(row => {
        console.log(`   • ${row.routine_name} (${row.routine_type})`);
      });
    }

    // Test the functions
    console.log('\n🧪 Testing Enhanced Functions:');
    
    // Test evaluate_shift_status function
    try {
      const testResult = await client.query(`
        SELECT evaluate_shift_status(1, CURRENT_TIMESTAMP) as test_status
      `);
      console.log('✅ evaluate_shift_status function working');
    } catch (error) {
      console.log('⚠️ evaluate_shift_status test failed (may be normal if no shifts exist)');
    }

    // Test time logic function
    try {
      const timeLogicResult = await client.query(`
        SELECT * FROM test_shift_time_logic('06:00:00', '18:00:00', '12:00:00', false)
      `);
      const result = timeLogicResult.rows[0];
      console.log(`✅ test_shift_time_logic function working: ${result.logic_used}`);
    } catch (error) {
      console.log('❌ test_shift_time_logic test failed:', error.message);
    }

    // Test status summary function
    try {
      const summaryResult = await client.query(`
        SELECT * FROM get_shift_status_summary(CURRENT_TIMESTAMP)
      `);
      const summary = summaryResult.rows[0];
      console.log(`✅ get_shift_status_summary function working: ${summary.total_shifts} total shifts`);
    } catch (error) {
      console.log('❌ get_shift_status_summary test failed:', error.message);
    }

    // Test bulk update function
    try {
      const updateResult = await client.query(`
        SELECT * FROM update_all_shift_statuses(CURRENT_TIMESTAMP)
      `);
      const stats = updateResult.rows[0];
      console.log(`✅ update_all_shift_statuses function working: ${stats.updated_count} updates`);
    } catch (error) {
      console.log('❌ update_all_shift_statuses test failed:', error.message);
    }

    console.log('\n🎉 Enhanced Shift Status Migration Completed Successfully!');
    console.log('\n🔧 Features Enabled:');
    console.log('   • Enhanced overnight logic for night shifts');
    console.log('   • Proper status evaluation (active > scheduled > completed)');
    console.log('   • Real-time status monitoring and updates');
    console.log('   • Performance optimized functions (<300ms target)');
    console.log('   • Immutable status protection (completed/cancelled)');
    console.log('   • Comprehensive testing and validation functions');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run migration if called directly
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('\n✅ Migration process completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Migration process failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigration };
