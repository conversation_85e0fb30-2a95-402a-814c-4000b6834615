#!/bin/bash

# ============================================================================
# DEPLOYMENT SCRIPTS VALIDATION TEST
# Validates all deployment scripts for syntax and basic functionality
# ============================================================================

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Logging
log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        "ERROR")   echo -e "${RED}❌ $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "INFO")    echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Test function
run_test() {
    local test_name=$1
    local test_command=$2
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "Testing $test_name... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo -e "${GREEN}PASS${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}FAIL${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
}

# Main validation
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    DEPLOYMENT SCRIPTS VALIDATION                            ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    log "INFO" "Validating deployment scripts..."
    
    # Test script existence
    run_test "Main deployment script exists" "test -f deploy-hauling-qr.sh"
    run_test "Uninstall script exists" "test -f uninstall-hauling-qr.sh"
    run_test "Health monitor script exists" "test -f health-monitor.sh"
    run_test "Config template exists" "test -f config-template.conf"
    run_test "README exists" "test -f DEPLOYMENT-README.md"
    
    # Test script permissions
    run_test "Main script is executable" "test -x deploy-hauling-qr.sh"
    run_test "Uninstall script is executable" "test -x uninstall-hauling-qr.sh"
    run_test "Health monitor is executable" "test -x health-monitor.sh"
    
    # Test script syntax
    run_test "Main script syntax" "bash -n deploy-hauling-qr.sh"
    run_test "Uninstall script syntax" "bash -n uninstall-hauling-qr.sh"
    run_test "Health monitor syntax" "bash -n health-monitor.sh"
    
    # Test help functions
    run_test "Main script help" "./deploy-hauling-qr.sh --help"
    run_test "Uninstall script help" "./uninstall-hauling-qr.sh --help"
    run_test "Health monitor help" "./health-monitor.sh --help"
    
    # Test dry-run functionality
    run_test "Main script dry-run" "timeout 30 ./deploy-hauling-qr.sh --dry-run --config-file config-template.conf"
    
    # Test configuration template
    run_test "Config template format" "grep -q 'REPO_URL=' config-template.conf"
    run_test "Config template has domain" "grep -q 'DOMAIN_NAME=' config-template.conf"
    run_test "Config template has passwords" "grep -q 'DB_PASSWORD=' config-template.conf"
    
    # Test documentation
    run_test "README has quick start" "grep -q 'Quick Start' DEPLOYMENT-README.md"
    run_test "README has troubleshooting" "grep -q 'Troubleshooting' DEPLOYMENT-README.md"
    run_test "README has examples" "grep -q 'Examples:' DEPLOYMENT-README.md"
    
    # Summary
    echo
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                              TEST RESULTS                                   ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    echo "📊 Test Summary:"
    echo "   Total Tests: $TOTAL_TESTS"
    echo "   Passed: $TESTS_PASSED"
    echo "   Failed: $TESTS_FAILED"
    echo "   Success Rate: $(( TESTS_PASSED * 100 / TOTAL_TESTS ))%"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo
        log "SUCCESS" "All tests passed! Deployment scripts are ready for use."
        echo
        echo "🚀 Next Steps:"
        echo "1. Copy scripts to your Ubuntu 24.04 VPS"
        echo "2. Edit config-template.conf with your settings"
        echo "3. Run: ./deploy-hauling-qr.sh --config-file config-template.conf"
        echo
        exit 0
    else
        echo
        log "ERROR" "$TESTS_FAILED tests failed. Please review and fix issues."
        exit 1
    fi
}

main "$@"
