#!/usr/bin/env node

/**
 * Shift Display Bug Reproduction Script
 * Reproduces the "No Active Shift" issue for <PERSON> (DR-002) on DT-100
 */

const { query, getClient } = require('../server/config/database');
const ShiftDisplayHelper = require('../server/utils/ShiftDisplayHelper');

async function reproduceBug() {
  console.log('🔍 Reproducing Shift Display Bug...');
  
  try {
    // 1. Get current production-like data
    const currentData = await query(`
      SELECT 
        ds.id as shift_id,
        ds.truck_id,
        dt.truck_number,
        ds.driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.shift_type,
        ds.start_time,
        ds.end_time,
        ds.start_date,
        ds.end_date,
        ds.status,
        ds.recurrence_pattern,
        ds.display_type,
        CURRENT_DATE as today,
        CURRENT_TIME as now
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
      WHERE dt.truck_number = 'DT-100'
        AND d.employee_id = 'DR-002'
        AND ds.status = 'active'
      ORDER BY ds.created_at DESC
      LIMIT 1
    `);

    if (currentData.rows.length === 0) {
      console.log('❌ No active shift found for Maria Garcia on DT-100');
      return;
    }

    const shift = currentData.rows[0];
    console.log('📋 Current Shift Data:', shift);

    // 2. Test ShiftDisplayHelper directly
    console.log('\n🔧 Testing ShiftDisplayHelper...');
    const displayInfo = await ShiftDisplayHelper.getCurrentDriverForDisplay(shift.truck_id);
    console.log('Display Helper Result:', displayInfo);

    // 3. Test the exact query used by ShiftDisplayHelper
    console.log('\n🔍 Testing exact query logic...');
    const exactQuery = `
      SELECT
        ds.id as shift_id,
        ds.driver_id,
        ds.shift_type,
        ds.display_type,
        ds.start_time,
        ds.end_time,
        d.full_name as driver_name,
        d.employee_id
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      WHERE ds.truck_id = $1
        AND ds.status = 'active'
        AND (
          (ds.recurrence_pattern = 'single' AND ds.shift_date = CURRENT_DATE)
          OR
          (ds.recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date)
        )
        AND (
          -- For regular shifts (same day)
          (ds.end_time >= ds.start_time AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
          OR
          -- For overnight shifts (crosses midnight)
          (ds.end_time < ds.start_time AND (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
        )
      ORDER BY ds.created_at DESC
      LIMIT 1
    `;

    const exactResult = await query(exactQuery, [shift.truck_id]);
    console.log('Exact Query Result:', exactResult.rows);

    // 4. Clear cache and retest
    console.log('\n🧹 Clearing cache and retesting...');
    ShiftDisplayHelper.clearAllCache();
    
    const displayInfoAfterClear = await ShiftDisplayHelper.getCurrentDriverForDisplay(shift.truck_id);
    console.log('After cache clear:', displayInfoAfterClear);

    // 5. Final verification
    if (displayInfoAfterClear.hasActiveShift && 
        displayInfoAfterClear.driver_name === 'Maria Garcia' && 
        displayInfoAfterClear.employee_id === 'DR-002') {
      console.log('✅ Bug reproduction: SHIFT DISPLAY WORKING CORRECTLY');
      console.log('✅ Expected: Maria Garcia (DR-002) 🌙 Night Shift');
      console.log('✅ Actual:', `${displayInfoAfterClear.driver_name} (${displayInfoAfterClear.employee_id}) ${displayInfoAfterClear.shift_type === 'night' ? '🌙' : '☀️'} ${displayInfoAfterClear.shift_type}`);
    } else {
      console.log('❌ Bug reproduction: SHIFT DISPLAY STILL BROKEN');
    }

  } catch (error) {
    console.error('❌ Error reproducing bug:', error);
  }
}

// CLI command
if (require.main === module) {
  reproduceBug().catch(console.error);
}

module.exports = { reproduceBug };