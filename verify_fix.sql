-- Verify that the fix has been applied correctly
-- Check the status of the problematic shift
SELECT 
    id,
    truck_id,
    driver_id,
    shift_type,
    status AS current_status,
    start_date,
    end_date,
    start_time,
    end_time,
    CURRENT_DATE AS current_date,
    CURRENT_TIME AS current_time,
    CURRENT_TIMESTAMP AS current_timestamp,
    -- Calculate the end datetime for the shift
    CASE 
        WHEN end_time < start_time THEN -- overnight shift
            (end_date + INTERVAL '1 day')::DATE + end_time
        ELSE -- day shift
            end_date::DATE + end_time
    END AS shift_end_datetime,
    -- Check if current timestamp is past the end datetime
    CURRENT_TIMESTAMP > 
        CASE 
            WHEN end_time < start_time THEN -- overnight shift
                (end_date + INTERVAL '1 day')::DATE + end_time
            ELSE -- day shift
                end_date::DATE + end_time
        END AS is_past_end_datetime
FROM 
    driver_shifts
WHERE 
    id = 536;

-- Test the evaluate_shift_status function to see if it now returns the correct status
SELECT 
    evaluate_shift_status(536) AS evaluated_status,
    (SELECT evaluated_status FROM debug_shift_status(536)) AS debug_status;

-- Test the schedule_auto_activation function to make sure it doesn't incorrectly complete the shift
-- First, let's call the function
SELECT schedule_auto_activation();

-- Then check if the shift status is still correct
SELECT 
    id,
    status AS current_status
FROM 
    driver_shifts
WHERE 
    id = 536;