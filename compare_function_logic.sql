-- Let's test the exact same logic step by step to find the discrepancy
-- First, let's check if the issue is in the immutable status check
SELECT 
    id,
    status,
    CASE 
        WHEN status IN ('completed', 'cancelled') THEN 'immutable_status_returned'
        ELSE 'status_can_be_updated'
    END AS immutable_check
FROM 
    driver_shifts
WHERE 
    id = 536;

-- Let's manually test the overnight shift end datetime calculation
SELECT 
    id,
    start_date,
    end_date,
    start_time,
    end_time,
    -- Original function logic for overnight shifts
    (end_date + INTERVAL '1 day')::DATE + end_time AS original_end_datetime,
    -- Current timestamp
    CURRENT_TIMESTAMP,
    -- Is past completion check
    CURRENT_TIMESTAMP > ((end_date + INTERVAL '1 day')::DATE + end_time) AS is_past_completion_original
FROM 
    driver_shifts
WHERE 
    id = 536;