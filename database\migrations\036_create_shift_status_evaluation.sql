-- Migration: Create comprehensive shift status evaluation function
-- Created: 2025-07-14
-- Purpose: Evaluate and update shift status based on date/time rules

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS evaluate_shift_status(INTEGER, TIMESTAMP);

-- Create the comprehensive shift status evaluation function
CREATE OR REPLACE FUNCTION evaluate_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) RETURNS TEXT AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_shift_start_datetime TIMESTAMP;
    v_shift_end_datetime TIMESTAMP;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_new_status TEXT;
BEGIN
    -- Get shift details
    SELECT 
        start_date,
        end_date,
        start_time,
        end_time,
        shift_type,
        status
    INTO v_shift
    FROM driver_shifts 
    WHERE id = p_shift_id;
    
    IF NOT FOUND THEN
        RETURN 'error';
    END IF;
    
    -- Extract current date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;
    
    -- Check if shift spans overnight
    v_is_overnight := v_shift.end_time < v_shift.start_time;
    
    -- Check if current date is within the shift's date range
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;
    
    -- Check if current time is within the shift's time window
    IF v_is_overnight THEN
        -- Overnight shift: spans across midnight
        -- Time window is from start_time to end_time next day
        IF v_current_time >= v_shift.start_time OR v_current_time < v_shift.end_time THEN
            v_is_within_time_window := TRUE;
        ELSE
            v_is_within_time_window := FALSE;
        END IF;
    ELSE
        -- Day shift: within same calendar day
        -- Time window is from start_time to end_time same day
        IF v_current_time >= v_shift.start_time AND v_current_time < v_shift.end_time THEN
            v_is_within_time_window := TRUE;
        ELSE
            v_is_within_time_window := FALSE;
        END IF;
    END IF;
    
    -- Determine new status based on rules
    IF v_is_within_date_range AND v_is_within_time_window THEN
        -- Rule 1: Active - within date range AND within time window
        v_new_status := 'active';
    ELSIF v_is_within_date_range AND NOT v_is_within_time_window THEN
        -- Rule 2: Scheduled - within date range BUT outside time window
        v_new_status := 'scheduled';
    ELSIF v_current_date > v_shift.end_date OR 
          (v_current_date = v_shift.end_date AND v_current_time >= v_shift.end_time) THEN
        -- Rule 3: Completed - past end date and end time
        v_new_status := 'completed';
    ELSE
        -- Default fallback
        v_new_status := 'scheduled';
    END IF;
    
    RETURN v_new_status;
END;
$$ LANGUAGE plpgsql;

-- Create function to update all shift statuses based on current time
CREATE OR REPLACE FUNCTION update_all_shift_statuses()
RETURNS INTEGER AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_shift_record RECORD;
    v_new_status TEXT;
BEGIN
    -- Process all shifts that need status updates
    FOR v_shift_record IN
        SELECT id, status
        FROM driver_shifts
        WHERE status != 'cancelled'
    LOOP
        -- Evaluate new status
        v_new_status := evaluate_shift_status(v_shift_record.id);
        
        -- Update if status has changed
        IF v_new_status != v_shift_record.status THEN
            UPDATE driver_shifts
            SET status = v_new_status::shift_status,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = v_shift_record.id;
            
            v_updated_count := v_updated_count + 1;
        END IF;
    END LOOP;
    
    RETURN v_updated_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to update specific shift status
CREATE OR REPLACE FUNCTION update_shift_status(p_shift_id INTEGER)
RETURNS TEXT AS $$
DECLARE
    v_new_status TEXT;
    v_old_status TEXT;
BEGIN
    -- Get current status
    SELECT status INTO v_old_status
    FROM driver_shifts
    WHERE id = p_shift_id;
    
    IF NOT FOUND THEN
        RETURN 'shift_not_found';
    END IF;
    
    -- Evaluate new status
    v_new_status := evaluate_shift_status(p_shift_id);
    
    -- Update if status has changed
    IF v_new_status != v_old_status THEN
        UPDATE driver_shifts
        SET status = v_new_status::shift_status,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = p_shift_id;
        
        RETURN v_new_status;
    END IF;
    
    RETURN v_old_status;
END;
$$ LANGUAGE plpgsql;

-- Create index for performance optimization
CREATE INDEX IF NOT EXISTS idx_driver_shifts_status_date 
ON driver_shifts(status, start_date, end_date);

-- Create index for time-based queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_time_range 
ON driver_shifts(start_time, end_time);

-- Comment on the functions
COMMENT ON FUNCTION evaluate_shift_status(INTEGER, TIMESTAMP) IS 
    'Evaluates the correct status for a shift based on current date/time rules';

COMMENT ON FUNCTION update_all_shift_statuses() IS 
    'Updates status for all shifts based on current date/time';

COMMENT ON FUNCTION update_shift_status(INTEGER) IS 
    'Updates status for a specific shift based on current date/time';