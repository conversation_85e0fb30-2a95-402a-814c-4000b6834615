#!/bin/bash

# ============================================================================
# HAULING QR TRIP SYSTEM - HEALTH MONITORING SCRIPT
# Comprehensive system and application health monitoring
# ============================================================================

set -euo pipefail

# Configuration
APP_DIR="/opt/hauling-qr-system"
APP_USER="hauling"
DB_NAME="hauling_qr_system"
DB_USER="hauling_user"
LOG_FILE="/var/log/hauling-qr-health.log"

# Thresholds
CPU_THRESHOLD=80
MEMORY_THRESHOLD=80
DISK_THRESHOLD=85
RESPONSE_TIME_THRESHOLD=5000  # milliseconds

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Options
VERBOSE=false
ALERT_MODE=false
JSON_OUTPUT=false

# Health status
OVERALL_STATUS="HEALTHY"
ISSUES_FOUND=()

# Logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    if [[ "$JSON_OUTPUT" == false ]]; then
        case $level in
            "ERROR")   echo -e "${RED}❌ $message${NC}" ;;
            "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
            "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
            "INFO")    echo -e "${BLUE}ℹ️  $message${NC}" ;;
        esac
    fi
}

# Add issue to list
add_issue() {
    local severity=$1
    local message=$2
    ISSUES_FOUND+=("$severity: $message")
    
    if [[ "$severity" == "CRITICAL" ]]; then
        OVERALL_STATUS="CRITICAL"
    elif [[ "$severity" == "WARNING" ]] && [[ "$OVERALL_STATUS" == "HEALTHY" ]]; then
        OVERALL_STATUS="WARNING"
    fi
}

# Check system resources
check_system_resources() {
    log "INFO" "Checking system resources..."
    
    # CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    cpu_usage=${cpu_usage%.*}  # Remove decimal part
    
    if [[ $cpu_usage -gt $CPU_THRESHOLD ]]; then
        add_issue "WARNING" "High CPU usage: ${cpu_usage}%"
    elif [[ "$VERBOSE" == true ]]; then
        log "SUCCESS" "CPU usage: ${cpu_usage}%"
    fi
    
    # Memory usage
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_mem * 100 / total_mem))
    
    if [[ $memory_usage -gt $MEMORY_THRESHOLD ]]; then
        add_issue "WARNING" "High memory usage: ${memory_usage}%"
    elif [[ "$VERBOSE" == true ]]; then
        log "SUCCESS" "Memory usage: ${memory_usage}%"
    fi
    
    # Disk usage
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    
    if [[ $disk_usage -gt $DISK_THRESHOLD ]]; then
        add_issue "CRITICAL" "High disk usage: ${disk_usage}%"
    elif [[ "$VERBOSE" == true ]]; then
        log "SUCCESS" "Disk usage: ${disk_usage}%"
    fi
    
    # Load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)
    local cpu_cores=$(nproc)
    local load_threshold=$((cpu_cores * 2))
    
    if (( $(echo "$load_avg > $load_threshold" | bc -l) )); then
        add_issue "WARNING" "High load average: $load_avg (cores: $cpu_cores)"
    elif [[ "$VERBOSE" == true ]]; then
        log "SUCCESS" "Load average: $load_avg"
    fi
}

# Check services
check_services() {
    log "INFO" "Checking services..."
    
    # PostgreSQL
    if systemctl is-active --quiet postgresql; then
        if [[ "$VERBOSE" == true ]]; then
            log "SUCCESS" "PostgreSQL is running"
        fi
    else
        add_issue "CRITICAL" "PostgreSQL is not running"
    fi
    
    # Nginx
    if systemctl is-active --quiet nginx; then
        if [[ "$VERBOSE" == true ]]; then
            log "SUCCESS" "Nginx is running"
        fi
    else
        add_issue "CRITICAL" "Nginx is not running"
    fi
    
    # PM2 Application
    if sudo -u "$APP_USER" pm2 list 2>/dev/null | grep -q "hauling-qr-system.*online"; then
        if [[ "$VERBOSE" == true ]]; then
            log "SUCCESS" "PM2 application is running"
        fi
    else
        add_issue "CRITICAL" "PM2 application is not running"
    fi
}

# Check database connectivity
check_database() {
    log "INFO" "Checking database connectivity..."
    
    # Read database password from .env file
    local db_password=""
    if [[ -f "$APP_DIR/.env" ]]; then
        db_password=$(grep "^DB_PASSWORD=" "$APP_DIR/.env" | cut -d'=' -f2)
    fi
    
    if [[ -n "$db_password" ]]; then
        if PGPASSWORD="$db_password" psql -h localhost -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
            if [[ "$VERBOSE" == true ]]; then
                log "SUCCESS" "Database connection successful"
            fi
        else
            add_issue "CRITICAL" "Database connection failed"
        fi
    else
        add_issue "WARNING" "Cannot read database password from .env file"
    fi
}

# Check application health
check_application() {
    log "INFO" "Checking application health..."
    
    # API health endpoint
    local start_time=$(date +%s%3N)
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response "http://localhost/health" 2>/dev/null || echo "000")
    local end_time=$(date +%s%3N)
    local response_time=$((end_time - start_time))
    
    if [[ "$response" == "200" ]]; then
        if [[ $response_time -gt $RESPONSE_TIME_THRESHOLD ]]; then
            add_issue "WARNING" "Slow API response: ${response_time}ms"
        elif [[ "$VERBOSE" == true ]]; then
            log "SUCCESS" "API health check passed (${response_time}ms)"
        fi
    else
        add_issue "CRITICAL" "API health check failed (HTTP $response)"
    fi
    
    # Frontend accessibility
    if curl -s "http://localhost/" | grep -q "<!doctype html>"; then
        if [[ "$VERBOSE" == true ]]; then
            log "SUCCESS" "Frontend is accessible"
        fi
    else
        add_issue "CRITICAL" "Frontend is not accessible"
    fi
}

# Check SSL certificate
check_ssl() {
    log "INFO" "Checking SSL certificate..."
    
    # Check if SSL is enabled
    local ssl_enabled=false
    if [[ -f "$APP_DIR/.env" ]]; then
        ssl_enabled=$(grep "^ENABLE_HTTPS=" "$APP_DIR/.env" | cut -d'=' -f2)
    fi
    
    if [[ "$ssl_enabled" == "true" ]]; then
        local domain=$(grep "^DOMAIN_NAME=" "$APP_DIR/.env" | cut -d'=' -f2 2>/dev/null || echo "")
        
        if [[ -n "$domain" ]]; then
            local cert_info=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "")
            
            if [[ -n "$cert_info" ]]; then
                local expiry_date=$(echo "$cert_info" | grep "notAfter" | cut -d'=' -f2)
                local expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null || echo "0")
                local current_timestamp=$(date +%s)
                local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
                
                if [[ $days_until_expiry -lt 30 ]]; then
                    add_issue "WARNING" "SSL certificate expires in $days_until_expiry days"
                elif [[ "$VERBOSE" == true ]]; then
                    log "SUCCESS" "SSL certificate valid for $days_until_expiry days"
                fi
            else
                add_issue "WARNING" "Cannot verify SSL certificate"
            fi
        fi
    elif [[ "$VERBOSE" == true ]]; then
        log "INFO" "SSL not enabled"
    fi
}

# Check log files for errors
check_logs() {
    log "INFO" "Checking application logs..."
    
    # Check for recent errors in application logs
    if [[ -f "$APP_DIR/logs/err.log" ]]; then
        local recent_errors=$(tail -100 "$APP_DIR/logs/err.log" | grep "$(date '+%Y-%m-%d')" | wc -l)
        
        if [[ $recent_errors -gt 10 ]]; then
            add_issue "WARNING" "$recent_errors errors found in today's logs"
        elif [[ "$VERBOSE" == true ]]; then
            log "SUCCESS" "Log file check passed ($recent_errors recent errors)"
        fi
    fi
    
    # Check Nginx error logs
    if [[ -f "/var/log/nginx/error.log" ]]; then
        local nginx_errors=$(tail -100 "/var/log/nginx/error.log" | grep "$(date '+%Y/%m/%d')" | wc -l)
        
        if [[ $nginx_errors -gt 5 ]]; then
            add_issue "WARNING" "$nginx_errors Nginx errors found today"
        elif [[ "$VERBOSE" == true ]]; then
            log "SUCCESS" "Nginx error log check passed ($nginx_errors recent errors)"
        fi
    fi
}

# Generate JSON output
generate_json_output() {
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    echo "{"
    echo "  \"timestamp\": \"$timestamp\","
    echo "  \"overall_status\": \"$OVERALL_STATUS\","
    echo "  \"issues\": ["
    
    local first=true
    for issue in "${ISSUES_FOUND[@]}"; do
        if [[ "$first" == false ]]; then
            echo ","
        fi
        echo -n "    \"$issue\""
        first=false
    done
    
    echo
    echo "  ]"
    echo "}"
}

# Show summary
show_summary() {
    if [[ "$JSON_OUTPUT" == true ]]; then
        generate_json_output
        return
    fi
    
    echo
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                           HEALTH CHECK SUMMARY                              ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    case $OVERALL_STATUS in
        "HEALTHY")
            echo -e "${GREEN}🎉 System Status: HEALTHY${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  System Status: WARNING${NC}"
            ;;
        "CRITICAL")
            echo -e "${RED}🚨 System Status: CRITICAL${NC}"
            ;;
    esac
    
    echo "📅 Check Time: $(date)"
    echo "🔍 Issues Found: ${#ISSUES_FOUND[@]}"
    
    if [[ ${#ISSUES_FOUND[@]} -gt 0 ]]; then
        echo
        echo "📋 Issues:"
        for issue in "${ISSUES_FOUND[@]}"; do
            echo "   • $issue"
        done
    fi
    
    echo
    echo "📊 Quick Stats:"
    echo "   • CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "   • Memory: $(free | awk 'NR==2{printf "%.1f%%", $3*100/$2 }')"
    echo "   • Disk: $(df / | awk 'NR==2 {print $5}')"
    echo "   • Load: $(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)"
    echo
}

# Show help
show_help() {
    echo "Hauling QR Trip System - Health Monitor"
    echo
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -v, --verbose           Show detailed output"
    echo "  -a, --alert             Alert mode (exit with error code if issues found)"
    echo "  -j, --json              Output results in JSON format"
    echo
    echo "Examples:"
    echo "  $0                      Basic health check"
    echo "  $0 --verbose            Detailed health check"
    echo "  $0 --alert              Health check with alert mode"
    echo "  $0 --json               JSON output for monitoring systems"
    echo
}

# Parse arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -a|--alert)
                ALERT_MODE=true
                shift
                ;;
            -j|--json)
                JSON_OUTPUT=true
                shift
                ;;
            *)
                echo "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Main function
main() {
    parse_arguments "$@"
    
    # Create log file if it doesn't exist
    sudo touch "$LOG_FILE"
    sudo chmod 644 "$LOG_FILE"
    
    # Run health checks
    check_system_resources
    check_services
    check_database
    check_application
    check_ssl
    check_logs
    
    # Show results
    show_summary
    
    # Exit with appropriate code in alert mode
    if [[ "$ALERT_MODE" == true ]]; then
        case $OVERALL_STATUS in
            "HEALTHY")
                exit 0
                ;;
            "WARNING")
                exit 1
                ;;
            "CRITICAL")
                exit 2
                ;;
        esac
    fi
}

main "$@"
