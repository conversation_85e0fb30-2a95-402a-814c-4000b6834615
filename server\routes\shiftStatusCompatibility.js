const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');

/**
 * Compatibility layer for new shift status management system
 * This integrates with the existing shifts.js implementation
 */

/**
 * @route   POST /api/shift-status/compatibility/update
 * @desc    Update shift statuses using new evaluation logic while maintaining compatibility
 * @access  Private (Admin)
 */
router.post('/compatibility/update', auth, async (req, res) => {
    try {
        const { referenceTime, dryRun } = req.body;
        
        // Use the new evaluation logic
        const result = await query(`
            SELECT update_all_shift_statuses() as updated_count
        `);
        
        const updatedCount = result.rows[0].updated_count;
        
        res.json({
            success: true,
            message: `Updated ${updatedCount} shift statuses using new evaluation logic`,
            data: {
                updated_count: updatedCount,
                method: 'new_evaluation_system'
            }
        });
        
    } catch (error) {
        console.error('Compatibility update error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update shift statuses',
            error: error.message
        });
    }
});

/**
 * @route   GET /api/shift-status/compatibility/compare
 * @desc    Compare old vs new status evaluation for debugging
 * @access  Private (Admin)
 */
router.get('/compatibility/compare', auth, async (req, res) => {
    try {
        const { shift_id } = req.query;
        
        let comparisonQuery;
        let params = [];
        
        if (shift_id) {
            // Compare specific shift
            comparisonQuery = `
                SELECT 
                    ds.id,
                    ds.start_date,
                    ds.end_date,
                    ds.start_time,
                    ds.end_time,
                    ds.shift_type,
                    ds.status as current_status,
                    evaluate_shift_status(ds.id) as new_status,
                    CASE
                        WHEN ds.status IN ('completed', 'cancelled') THEN ds.status
                        WHEN CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
                            AND (
                                (ds.end_time > ds.start_time AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
                                OR
                                (ds.end_time < ds.start_time AND (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
                            )
                        THEN 'active'
                        WHEN CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
                        THEN 'scheduled'
                        WHEN CURRENT_DATE > ds.end_date OR
                             (CURRENT_DATE = ds.end_date AND
                              CASE
                                WHEN ds.end_time < ds.start_time THEN CURRENT_TIME > ds.end_time
                                ELSE CURRENT_TIME > ds.end_time AND CURRENT_TIME < ds.start_time
                              END)
                        THEN 'completed'
                        ELSE ds.status
                    END as old_logic_status
                FROM driver_shifts ds
                WHERE ds.id = $1
            `;
            params = [shift_id];
        } else {
            // Compare all shifts
            comparisonQuery = `
                SELECT 
                    ds.id,
                    ds.start_date,
                    ds.end_date,
                    ds.start_time,
                    ds.end_time,
                    ds.shift_type,
                    ds.status as current_status,
                    evaluate_shift_status(ds.id) as new_status,
                    CASE
                        WHEN ds.status IN ('completed', 'cancelled') THEN ds.status
                        WHEN CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
                            AND (
                                (ds.end_time > ds.start_time AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
                                OR
                                (ds.end_time < ds.start_time AND (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
                            )
                        THEN 'active'
                        WHEN CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
                        THEN 'scheduled'
                        WHEN CURRENT_DATE > ds.end_date OR
                             (CURRENT_DATE = ds.end_date AND
                              CASE
                                WHEN ds.end_time < ds.start_time THEN CURRENT_TIME > ds.end_time
                                ELSE CURRENT_TIME > ds.end_time AND CURRENT_TIME < ds.start_time
                              END)
                        THEN 'completed'
                        ELSE ds.status
                    END as old_logic_status
                FROM driver_shifts ds
                WHERE ds.status != 'cancelled'
                ORDER BY ds.id DESC
                LIMIT 50
            `;
        }
        
        const result = await query(comparisonQuery, params);
        
        const differences = result.rows.filter(row => row.new_status !== row.old_logic_status);
        
        res.json({
            success: true,
            data: {
                total_shifts: result.rows.length,
                differences: differences.length,
                shifts: result.rows,
                differences_detail: differences
            }
        });
        
    } catch (error) {
        console.error('Compatibility comparison error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to compare status evaluations',
            error: error.message
        });
    }
});

/**
 * @route   POST /api/shift-status/compatibility/migrate
 * @desc    Migrate from old to new status evaluation system
 * @access  Private (Admin)
 */
router.post('/compatibility/migrate', auth, async (req, res) => {
    try {
        const { batch_size = 100 } = req.body;
        
        // Get shifts that might need status updates
        const shiftsToUpdate = await query(`
            SELECT id, status, start_date, end_date, start_time, end_time
            FROM driver_shifts
            WHERE status IN ('scheduled', 'active')
            ORDER BY id
            LIMIT $1
        `, [batch_size]);
        
        let updatedCount = 0;
        const updates = [];
        
        for (const shift of shiftsToUpdate.rows) {
            const newStatus = await query(`
                SELECT evaluate_shift_status($1) as new_status
            `, [shift.id]);
            
            const evaluatedStatus = newStatus.rows[0].new_status;
            
            if (evaluatedStatus !== shift.status) {
                await query(`
                    UPDATE driver_shifts
                    SET status = $1, updated_at = CURRENT_TIMESTAMP
                    WHERE id = $2
                `, [evaluatedStatus, shift.id]);
                
                updatedCount++;
                updates.push({
                    shift_id: shift.id,
                    old_status: shift.status,
                    new_status: evaluatedStatus
                });
            }
        }
        
        res.json({
            success: true,
            message: `Migrated ${updatedCount} shifts to new evaluation system`,
            data: {
                processed: shiftsToUpdate.rows.length,
                updated: updatedCount,
                updates: updates
            }
        });
        
    } catch (error) {
        console.error('Migration error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to migrate shift statuses',
            error: error.message
        });
    }
});

module.exports = router;