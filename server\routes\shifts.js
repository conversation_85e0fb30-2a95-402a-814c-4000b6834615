const express = require('express');
const router = express.Router();
const { query, getClient } = require('../config/database');
const auth = require('../middleware/auth');
const { shiftTransitionManager } = require('../utils/enhanced-shift-transitions');
const { broadcast } = require('../websocket');
const Joi = require('joi');
const ShiftDisplayHelper = require('../utils/ShiftDisplayHelper');
const shiftSyncMonitor = require('../utils/SimpleShiftSyncMonitor');

// Enhanced validation schemas for date range support
const shiftSchema = Joi.object({
  truck_id: Joi.number().integer().positive().required(),
  driver_id: Joi.number().integer().positive().required(),
  shift_type: Joi.string().valid('day', 'night', 'custom').required(),
  display_type: Joi.string().valid('day', 'night', 'custom').optional(),
  mode: Joi.string().valid('single', 'range').default('single').optional(),

  // Date fields - either single date or date range
  shift_date: Joi.date().optional(),
  start_date: Joi.date().optional(),
  end_date: Joi.date().optional(),
  recurrence_pattern: Joi.string().valid('single', 'daily', 'weekly', 'weekdays', 'weekends', 'custom').default('single'),

  start_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/).required().messages({
    'string.pattern.base': 'start_time must be in HH:MM or HH:MM:SS format'
  }),
  end_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/).required().messages({
    'string.pattern.base': 'end_time must be in HH:MM or HH:MM:SS format'
  }),
  status: Joi.string().valid('scheduled', 'active', 'completed', 'cancelled').optional(),
  handover_notes: Joi.string().max(1000).optional().allow(''),
  create_bulk_daily_shifts: Joi.boolean().optional().default(false),
  overwrite_existing: Joi.boolean().optional().default(false)
}).custom((value, helpers) => {
  // Enhanced validation for unified date range approach
  const { mode, recurrence_pattern, shift_date, start_date, end_date } = value;

  // Unified approach: Always require start_date and end_date
  if (!start_date || !end_date) {
    // Fallback: If shift_date is provided (legacy support), use it for both start and end
    if (shift_date) {
      value.start_date = shift_date;
      value.end_date = shift_date;
    } else {
      return helpers.error('custom.dateRangeRequired');
    }
  }

  // Validate date range
  const startDateObj = new Date(value.start_date);
  const endDateObj = new Date(value.end_date);

  if (endDateObj < startDateObj) {
    return helpers.error('custom.invalidDateRange');
  }

  // Auto-detect recurrence pattern based on date range
  if (value.start_date === value.end_date) {
    value.recurrence_pattern = 'single';
  } else {
    value.recurrence_pattern = 'custom';
  }

  return value;
}, 'Unified date range validation').messages({
  'custom.dateRangeRequired': 'start_date and end_date are required for shift creation',
  'custom.invalidDateRange': 'end_date must be after or equal to start_date'
});

// Flexible update schema - all fields optional except validation rules
const updateShiftSchema = Joi.object({
  truck_id: Joi.number().integer().positive().optional(),
  driver_id: Joi.number().integer().positive().optional(),
  shift_type: Joi.string().valid('day', 'night').optional(),
  start_date: Joi.date().iso().optional(),
  end_date: Joi.date().iso().optional(),
  start_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/).optional().messages({
    'string.pattern.base': 'start_time must be in HH:MM or HH:MM:SS format'
  }),
  end_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/).optional().messages({
    'string.pattern.base': 'end_time must be in HH:MM or HH:MM:SS format'
  }),
  status: Joi.string().valid('scheduled', 'active', 'completed', 'cancelled').optional(),
  handover_notes: Joi.string().allow('').optional(),
  assignment_id: Joi.number().integer().positive().allow(null).optional()
}).min(1).messages({
  'object.min': 'At least one field must be provided for update'
});

// Status-only update schema for shift status changes
const statusUpdateSchema = Joi.object({
  status: Joi.string().valid('scheduled', 'active', 'completed', 'cancelled').required()
}).messages({
  'any.only': 'Status must be one of: scheduled, active, completed, cancelled',
  'any.required': 'Status is required for status updates'
});

// @route   GET /api/shifts
// @desc    Get all shifts with filtering options
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    console.log('SHIFT_GET_REQUEST', 'Received shift retrieval request', {
      query: req.query,
      user_id: req.user.id
    });

    const {
      // Single filters
      truck_id,
      driver_id,
      shift_date,
      status,
      shift_type,
      start_date,
      end_date,

      // Multi-select filters (comma-separated)
      truck_ids,
      driver_ids,
      statuses,
      shift_types,

      // Date range filters
      date_from,
      date_to,

      // Pagination and sorting
      limit = 50,
      offset = 0,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;

    // Build dynamic WHERE clause with enhanced filtering

    // Single truck filter
    if (truck_id) {
      whereConditions.push(`ds.truck_id = $${paramIndex}`);
      queryParams.push(truck_id);
      paramIndex++;
    }

    // Multi-truck filter
    if (truck_ids) {
      const truckIdArray = truck_ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      if (truckIdArray.length > 0) {
        const placeholders = truckIdArray.map(() => `$${paramIndex++}`).join(',');
        whereConditions.push(`ds.truck_id IN (${placeholders})`);
        queryParams.push(...truckIdArray);
      }
    }

    // Single driver filter
    if (driver_id) {
      whereConditions.push(`ds.driver_id = $${paramIndex}`);
      queryParams.push(driver_id);
      paramIndex++;
    }

    // Multi-driver filter
    if (driver_ids) {
      const driverIdArray = driver_ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      if (driverIdArray.length > 0) {
        const placeholders = driverIdArray.map(() => `$${paramIndex++}`).join(',');
        whereConditions.push(`ds.driver_id IN (${placeholders})`);
        queryParams.push(...driverIdArray);
      }
    }

    // Single date filter - UNIFIED: Check if date falls within shift range (works for both single and multi-day)
    if (shift_date) {
      whereConditions.push(`($${paramIndex} BETWEEN ds.start_date AND ds.end_date)`);
      queryParams.push(shift_date);
      paramIndex++;
    }

    // Date range filter - UNIFIED APPROACH: Show shifts that overlap with the date range
    if (date_from && date_to) {
      whereConditions.push(`(ds.start_date <= $${paramIndex + 1} AND ds.end_date >= $${paramIndex})`);
      queryParams.push(date_from, date_to);
      paramIndex += 2;
    } else if (date_from) {
      whereConditions.push(`ds.end_date >= $${paramIndex}`);
      queryParams.push(date_from);
      paramIndex++;
    } else if (date_to) {
      whereConditions.push(`ds.start_date <= $${paramIndex}`);
      queryParams.push(date_to);
      paramIndex++;
    }

    // Single status filter
    if (status) {
      whereConditions.push(`ds.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    // Multi-status filter
    if (statuses) {
      const statusArray = statuses.split(',').map(s => s.trim()).filter(s => s);
      if (statusArray.length > 0) {
        const placeholders = statusArray.map(() => `$${paramIndex++}`).join(',');
        whereConditions.push(`ds.status IN (${placeholders})`);
        queryParams.push(...statusArray);
      }
    }

    // Single shift type filter
    if (shift_type) {
      whereConditions.push(`ds.shift_type = $${paramIndex}`);
      queryParams.push(shift_type);
      paramIndex++;
    }

    // Multi-shift type filter
    if (shift_types) {
      const shiftTypeArray = shift_types.split(',').map(s => s.trim()).filter(s => s);
      if (shiftTypeArray.length > 0) {
        const placeholders = shiftTypeArray.map(() => `$${paramIndex++}`).join(',');
        whereConditions.push(`ds.shift_type IN (${placeholders})`);
        queryParams.push(...shiftTypeArray);
      }
    }

    // Build WHERE clause
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Validate sort parameters
    const validSortColumns = ['created_at', 'shift_date', 'start_date', 'end_date', 'start_time', 'truck_number', 'driver_name', 'status'];
    const validSortOrders = ['ASC', 'DESC'];

    const sortColumn = validSortColumns.includes(sort_by) ? sort_by : 'created_at';
    const sortOrder = validSortOrders.includes(sort_order.toUpperCase()) ? sort_order.toUpperCase() : 'DESC';

    // Add pagination parameters
    const limitValue = Math.min(parseInt(limit) || 50, 100); // Max 100 records
    const offsetValue = Math.max(parseInt(offset) || 0, 0);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total_count
      FROM driver_shifts ds
      LEFT JOIN dump_trucks dt ON ds.truck_id = dt.id
      LEFT JOIN drivers d ON ds.driver_id = d.id
      LEFT JOIN assignments a ON ds.assignment_id = a.id
      ${whereClause}
    `;

    const shiftsQuery = `
      SELECT
        ds.id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        ds.shift_date,
        ds.start_date,
        ds.end_date,
        ds.recurrence_pattern,
        ds.display_type,
        ds.start_time,
        ds.end_time,
        ds.status as stored_status,

        -- Real-time calculated status (never override stored completed/cancelled)
        CASE
          -- Never override stored completed or cancelled status
          WHEN ds.status IN ('completed', 'cancelled') THEN ds.status
          
          -- Auto-transition logic for scheduled/active shifts only
          WHEN ds.status IN ('scheduled', 'active') THEN
            CASE
              -- Auto-activate when current timestamp is within range
              WHEN CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
                AND (
                  -- Normal time range (start_time < end_time)
                  (ds.end_time > ds.start_time AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
                  OR
                  -- Overnight time range (start_time > end_time)
                  (ds.end_time < ds.start_time AND (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
                )
              THEN 'active'
              
              -- Auto-schedule when outside time range but within date range
              WHEN CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
              THEN 'scheduled'
              
              -- Auto-complete when past end date/time
              WHEN CURRENT_DATE > ds.end_date OR
                   (CURRENT_DATE = ds.end_date AND
                    CASE
                      WHEN ds.end_time > ds.start_time THEN CURRENT_TIME > ds.end_time
                      ELSE CURRENT_TIME > ds.end_time AND CURRENT_TIME < ds.start_time
                    END)
              THEN 'completed'
              
              -- Default to stored status
              ELSE ds.status
            END
          
          -- For any other status, return stored status
          ELSE ds.status
        END as status,
        ds.previous_shift_id,
        ds.handover_notes,
        ds.handover_completed_at,
        ds.assignment_id,
        ds.auto_created,
        ds.created_at,
        ds.updated_at,
        dt.truck_number,
        dt.license_plate,
        d.full_name as driver_name,
        d.employee_id,
        a.assignment_code,
        a.status as assignment_status
      FROM driver_shifts ds
      LEFT JOIN dump_trucks dt ON ds.truck_id = dt.id
      LEFT JOIN drivers d ON ds.driver_id = d.id
      LEFT JOIN assignments a ON ds.assignment_id = a.id
      ${whereClause}
      ORDER BY ${sortColumn === 'driver_name' ? 'd.full_name' : sortColumn === 'truck_number' ? 'dt.truck_number' : 'ds.' + sortColumn} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Execute both queries
    const [countResult, shiftsResult] = await Promise.all([
      query(countQuery, queryParams),
      query(shiftsQuery, [...queryParams, limitValue, offsetValue])
    ]);

    const totalCount = parseInt(countResult.rows[0].total_count);
    const totalPages = Math.ceil(totalCount / limitValue);
    const currentPage = Math.floor(offsetValue / limitValue) + 1;

    console.log('SHIFT_GET_SUCCESS', 'Shifts retrieved successfully', {
      total_count: totalCount,
      returned_count: shiftsResult.rows.length,
      current_page: currentPage,
      total_pages: totalPages,
      filters_applied: Object.keys(req.query).length > 0
    });

    res.json({
      success: true,
      data: shiftsResult.rows,
      pagination: {
        total_count: totalCount,
        returned_count: shiftsResult.rows.length,
        current_page: currentPage,
        total_pages: totalPages,
        limit: limitValue,
        offset: offsetValue,
        has_next: currentPage < totalPages,
        has_previous: currentPage > 1
      },
      filters: {
        applied: Object.keys(req.query).filter(key => !['limit', 'offset', 'sort_by', 'sort_order'].includes(key)),
        total_without_filters: totalCount // This could be enhanced to show unfiltered count
      }
    });

  } catch (error) {
    console.error('Get shifts error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve shifts'
    });
  }
});

// @route   GET /api/shifts/current/:truck_id
// @desc    Get current active shift for a truck
// @access  Private
router.get('/current/:truck_id', auth, async (req, res) => {
  try {
    const { truck_id } = req.params;

    const currentShiftQuery = `
      SELECT 
        ds.id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        ds.shift_date,
        ds.start_time,
        ds.end_time,
        ds.status,
        ds.assignment_id,
        dt.truck_number,
        d.full_name as driver_name,
        d.employee_id,
        a.assignment_code
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
      LEFT JOIN assignments a ON ds.assignment_id = a.id
      WHERE ds.truck_id = $1
        AND ds.status = 'active'
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        AND CURRENT_TIME BETWEEN ds.start_time AND 
            CASE 
              WHEN ds.end_time < ds.start_time 
              THEN ds.end_time + interval '24 hours'
              ELSE ds.end_time 
            END
      ORDER BY ds.created_at DESC
      LIMIT 1
    `;

    const result = await query(currentShiftQuery, [truck_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'No active shift found for this truck'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get current shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve current shift'
    });
  }
});

// @route   POST /api/shifts
// @desc    Create new shift
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    console.log('SHIFT_CREATE_REQUEST', 'Received shift creation request', {
      body: req.body,
      recurrence_pattern: req.body.recurrence_pattern,
      shift_date: req.body.shift_date,
      start_date: req.body.start_date,
      end_date: req.body.end_date
    });

    // Validate input
    const { error } = shiftSchema.validate(req.body);
    if (error) {
      console.log('SHIFT_VALIDATION_ERROR', 'Validation failed', {
        error: error.details[0].message,
        body: req.body
      });
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      truck_id,
      driver_id,
      shift_type,
      display_type,
      shift_date,
      start_date,
      end_date,
      recurrence_pattern = 'single',
      start_time,
      end_time,
      status = 'scheduled',
      handover_notes = '',
      mode = 'single' // 'single' or 'range'
    } = req.body;

    console.log('SHIFT_CREATE_EXTRACTED_DATA', 'Extracted data from request', {
      truck_id,
      driver_id,
      shift_type,
      display_type,
      shift_date,
      start_date,
      end_date,
      recurrence_pattern,
      start_time,
      end_time,
      status,
      handover_notes,
      mode
    });

    // Unified date range validation (already handled by Joi schema)
    const startDateObj = new Date(start_date);
    const endDateObj = new Date(end_date);

    // Check for max 31 days
    const diffTime = Math.abs(endDateObj - startDateObj);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    if (diffDays > 31) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Date range cannot exceed 31 days'
      });
    }

    // UNIFIED APPROACH: Always use start_date/end_date range
    // Single day: start_date === end_date
    // Multi-day: start_date !== end_date
    const isSingleDay = start_date === end_date;

    // Check if user wants bulk daily shifts (multiple rows) or unified shift (single row)
    const createBulkDailyShifts = req.body.create_bulk_daily_shifts === true;

    console.log('SHIFT_CREATION_UNIFIED', 'Using unified date range approach', {
      isSingleDay,
      createBulkDailyShifts,
      start_date,
      end_date,
      approach: createBulkDailyShifts ? 'bulk_daily_rows' : 'unified_single_row'
    });

    // Only create bulk daily shifts if explicitly requested
    if (createBulkDailyShifts && !isSingleDay) {
      const client = await getClient();

      try {
        await client.query('BEGIN');

        const createdShifts = [];
        const conflicts = [];

        // Generate dates for the range
        const currentDate = new Date(start_date);
        const endDateObj = new Date(end_date);

        while (currentDate <= endDateObj) {
          const dateStr = currentDate.toISOString().split('T')[0];

          // Enhanced overlap detection - check for actual time conflicts
          const overlapCheck = await client.query(`
            SELECT id, shift_type, start_time, end_time, shift_date FROM driver_shifts
            WHERE truck_id = $1
              AND driver_id = $2
              AND status IN ('active', 'scheduled')
              AND shift_date = $3
          `, [truck_id, driver_id, dateStr]);

          // Check for actual time conflicts (not just same date)
          let hasTimeConflict = false;
          const newStartTime = start_time;
          const newEndTime = end_time;

          for (const existingShift of overlapCheck.rows) {
            const existingStart = existingShift.start_time;
            const existingEnd = existingShift.end_time;

            // Check if times actually overlap
            // Handle overnight shifts (end time < start time)
            const newIsOvernight = newEndTime < newStartTime;
            const existingIsOvernight = existingEnd < existingStart;

            if (newIsOvernight || existingIsOvernight) {
              // For overnight shifts, more complex logic needed
              // For now, allow day/night combinations on same date
              if (shift_type !== existingShift.shift_type) {
                continue; // Allow different shift types on same date
              }
            }

            // Simple time overlap check for same-day shifts
            if (!newIsOvernight && !existingIsOvernight) {
              if ((newStartTime < existingEnd && newEndTime > existingStart)) {
                hasTimeConflict = true;
                break;
              }
            } else {
              // For overnight shifts, be more permissive
              if (shift_type === existingShift.shift_type) {
                hasTimeConflict = true;
                break;
              }
            }
          }

          if (hasTimeConflict) {
            conflicts.push({
              date: dateStr,
              conflictingShifts: overlapCheck.rows
            });
          } else {
            // Create shift for this date with preserved start_date and end_date
            const result = await client.query(`
              INSERT INTO driver_shifts
              (truck_id, driver_id, shift_type, display_type, shift_date, start_date, end_date, start_time, end_time, status, handover_notes, recurrence_pattern)
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
              RETURNING *
            `, [
              truck_id,
              driver_id,
              shift_type,
              display_type || shift_type,
              dateStr,
              start_date, // Preserve original start_date
              end_date,   // Preserve original end_date
              start_time,
              end_time,
              status,
              handover_notes,
              'custom' // Mark as custom since it's part of a multi-day range
            ]);

            createdShifts.push(result.rows[0]);
          }

          currentDate.setDate(currentDate.getDate() + 1);
        }

        if (conflicts.length > 0) {
          await client.query('ROLLBACK');

          // Check if user wants to overwrite existing shifts
          const overwriteExisting = req.body.overwrite_existing === true;

          if (!overwriteExisting) {
            return res.status(400).json({
              error: 'Conflict Error',
              message: `Conflicts found on ${conflicts.length} date(s)`,
              conflicts: conflicts.map(c => ({
                date: c.date,
                message: `Existing ${c.conflictingShifts[0]?.shift_type || 'shift'} shift on ${c.date}`,
                existing_shifts: c.conflictingShifts.map(s => ({
                  id: s.id,
                  shift_type: s.shift_type,
                  start_time: s.start_time,
                  end_time: s.end_time,
                  status: s.status
                }))
              })),
              resolution_options: {
                overwrite_existing: 'Set overwrite_existing: true to replace existing shifts',
                skip_conflicts: 'Use partial creation endpoint to skip conflicting dates',
                update_existing: 'Use update endpoint to modify existing shifts'
              }
            });
          }

          // If overwrite_existing is true, delete conflicting shifts and proceed
          console.log('SHIFT_OVERWRITE_MODE', 'Overwriting existing shifts', {
            conflicts_count: conflicts.length,
            truck_id,
            driver_id
          });

          // Delete conflicting shifts
          for (const conflict of conflicts) {
            for (const existingShift of conflict.conflictingShifts) {
              await client.query('DELETE FROM driver_shifts WHERE id = $1', [existingShift.id]);
            }
          }

          // Reset conflicts array and restart creation process
          conflicts.length = 0;

          // Restart the creation process after clearing conflicts
          const restartDate = new Date(start_date);
          while (restartDate <= endDateObj) {
            const dateStr = restartDate.toISOString().split('T')[0];

            // Create shift for this date with preserved start_date and end_date
            const result = await client.query(`
              INSERT INTO driver_shifts
              (truck_id, driver_id, shift_type, display_type, shift_date, start_date, end_date, start_time, end_time, status, handover_notes, recurrence_pattern)
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
              RETURNING *
            `, [
              truck_id,
              driver_id,
              shift_type,
              display_type || shift_type,
              dateStr,
              start_date,
              end_date,
              start_time,
              end_time,
              status || 'scheduled',
              handover_notes || null,
              recurrence_pattern || 'daily'
            ]);

            createdShifts.push(result.rows[0]);
            restartDate.setDate(restartDate.getDate() + 1);
          }
        }

        await client.query('COMMIT');

        console.log('BULK_SHIFT_CREATE_SUCCESS', 'Bulk shifts created successfully', {
          count: createdShifts.length,
          date_range: `${start_date} to ${end_date}`,
          truck_id,
          driver_id
        });

        // Send WebSocket notification for bulk shift creation
        broadcast({
          type: 'bulk_shifts_created',
          title: 'Bulk Shifts Created',
          message: `${createdShifts.length} ${shift_type} shifts created for Truck ${truck_id} (${start_date} to ${end_date})`,
          data: {
            count: createdShifts.length,
            truck_id,
            driver_id,
            shift_type,
            start_date,
            end_date,
            shifts: createdShifts.map(s => ({
              id: s.id,
              shift_date: s.shift_date,
              start_time: s.start_time,
              end_time: s.end_time
            }))
          },
          timestamp: new Date().toISOString(),
          priority: 'medium'
        });

        return res.status(201).json({
          success: true,
          message: `${createdShifts.length} shifts created successfully`,
          data: createdShifts
        });

      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }
    }

    // Enhanced single/multi-day shift creation with intelligent overlap detection
    console.log('SHIFT_OVERLAP_CHECK', 'Checking for overlaps', {
      truck_id,
      driver_id,
      start_date,
      end_date,
      isSingleDay
    });

    const overlapCheck = await query(`
      SELECT id, shift_type, start_time, end_time, shift_date, start_date, end_date FROM driver_shifts
      WHERE truck_id = $1
        AND driver_id = $2
        AND status IN ('active', 'scheduled')
        AND (
          -- Check for date range overlaps
          (shift_date IS NOT NULL AND shift_date BETWEEN $3 AND $4) OR
          (start_date IS NOT NULL AND end_date IS NOT NULL AND
           NOT (end_date < $3 OR start_date > $4))
        )
    `, [truck_id, driver_id, start_date, end_date]);

    console.log('SHIFT_OVERLAP_RESULT', 'Overlap check completed', {
      overlapping_shifts: overlapCheck.rows.length,
      overlaps: overlapCheck.rows
    });

    // Enhanced overlap detection - check for actual time conflicts
    let hasTimeConflict = false;
    const newStartTime = start_time;
    const newEndTime = end_time;

    for (const existingShift of overlapCheck.rows) {
      const existingStart = existingShift.start_time;
      const existingEnd = existingShift.end_time;

      // Check if times actually overlap
      // Handle overnight shifts (end time < start time)
      const newIsOvernight = newEndTime < newStartTime;
      const existingIsOvernight = existingEnd < existingStart;

      if (newIsOvernight || existingIsOvernight) {
        // For overnight shifts, more complex logic needed
        // For now, allow day/night combinations on same date
        if (shift_type !== existingShift.shift_type) {
          continue; // Allow different shift types on same date
        }
      }

      // Simple time overlap check for same-day shifts
      if (!newIsOvernight && !existingIsOvernight) {
        if ((newStartTime < existingEnd && newEndTime > existingStart)) {
          hasTimeConflict = true;
          break;
        }
      } else {
        // For overnight shifts, be more permissive
        if (shift_type === existingShift.shift_type) {
          hasTimeConflict = true;
          break;
        }
      }
    }

    if (hasTimeConflict) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Shift overlaps with existing shift for this truck and driver'
      });
    }

    // Smart auto-activation: Determine initial status based on current date/time
    const currentDate = new Date();
    const currentDateStr = currentDate.toISOString().split('T')[0];
    const currentTimeStr = currentDate.toTimeString().split(' ')[0].substring(0, 8); // HH:MM:SS format

    // Normalize time formats (add :00 seconds if not present)
    const normalizedStartTime = start_time.includes(':') && start_time.split(':').length === 2 ? `${start_time}:00` : start_time;
    const normalizedEndTime = end_time.includes(':') && end_time.split(':').length === 2 ? `${end_time}:00` : end_time;

    console.log('SHIFT_AUTO_ACTIVATION_CHECK', 'Checking auto-activation conditions', {
      current_date: currentDateStr,
      current_time: currentTimeStr,
      shift_start_date: start_date,
      shift_end_date: end_date,
      shift_start_time: normalizedStartTime,
      shift_end_time: normalizedEndTime,
      original_status: status
    });

    // Check if current date is within shift date range
    const isDateInRange = currentDateStr >= start_date && currentDateStr <= end_date;

    // Check if current time is within shift time range (handle overnight shifts)
    let isTimeInRange = false;
    if (normalizedEndTime < normalizedStartTime) {
      // Overnight shift (crosses midnight)
      isTimeInRange = currentTimeStr >= normalizedStartTime || currentTimeStr <= normalizedEndTime;
    } else {
      // Regular shift (same day)
      isTimeInRange = currentTimeStr >= normalizedStartTime && currentTimeStr <= normalizedEndTime;
    }

    // Auto-determine status based on current date/time
    let finalStatus = status; // Use a new variable to avoid const reassignment
    if (isDateInRange && isTimeInRange) {
      finalStatus = 'active';
      console.log('SHIFT_AUTO_ACTIVATED', 'Shift auto-activated due to current date/time being within range');
    } else if (status !== 'scheduled') {
      // Only override if not explicitly set to scheduled
      finalStatus = 'scheduled';
      console.log('SHIFT_AUTO_SCHEDULED', 'Shift set to scheduled (outside active range)');
    }

    // UNIFIED SHIFT CREATION: shift_date will be auto-synced with start_date by trigger
    const result = await query(`
      INSERT INTO driver_shifts
      (truck_id, driver_id, shift_type, display_type, shift_date, start_date, end_date, recurrence_pattern, start_time, end_time, status, handover_notes)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `, [
      truck_id,
      driver_id,
      shift_type,
      display_type || shift_type,
      start_date, // Will be auto-synced by trigger, but set explicitly for clarity
      start_date,
      end_date,
      isSingleDay ? 'single' : 'custom', // Single day still marked as 'single' for compatibility
      start_time,
      end_time,
      finalStatus,
      handover_notes
    ]);

    // Get complete shift details with related data
    const shiftDetails = await query(`
      SELECT 
        ds.*,
        dt.truck_number,
        d.full_name as driver_name,
        d.employee_id
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
      WHERE ds.id = $1
    `, [result.rows[0].id]);

    res.status(201).json({
      success: true,
      message: 'Shift created successfully',
      data: shiftDetails.rows[0]
    });

  } catch (error) {
    console.error('SHIFT_CREATE_ERROR', 'Create shift error:', {
      error: error.message,
      stack: error.stack,
      request_body: req.body
    });
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create shift',
      debug: error.message
    });
  }
});

// ============================================================================
// SHIFT SYNCHRONIZATION MONITORING ENDPOINTS (MUST BE BEFORE /:id ROUTE)
// ============================================================================

// @route   GET /api/shifts/sync-status
// @desc    Get shift synchronization monitoring status
// @access  Private
router.get('/sync-status', auth, async (req, res) => {
  try {
    const status = shiftSyncMonitor.getStatus();

    res.json({
      success: true,
      data: status,
      message: 'Synchronization status retrieved successfully'
    });

  } catch (error) {
    console.error('Sync status error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve synchronization status'
    });
  }
});

// @route   POST /api/shifts/sync-check
// @desc    Force a manual synchronization check
// @access  Private
router.post('/sync-check', auth, async (req, res) => {
  try {
    console.log('SHIFT_SYNC_API', 'Manual synchronization check requested');

    const status = await shiftSyncMonitor.forceCheck();

    res.json({
      success: true,
      data: status,
      message: 'Manual synchronization check completed'
    });

  } catch (error) {
    console.error('Manual sync check error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to perform manual synchronization check'
    });
  }
});

// @route   POST /api/shifts/sync-start
// @desc    Start the synchronization monitoring
// @access  Private
router.post('/sync-start', auth, async (req, res) => {
  try {
    shiftSyncMonitor.start();

    res.json({
      success: true,
      data: shiftSyncMonitor.getStatus(),
      message: 'Synchronization monitoring started'
    });

  } catch (error) {
    console.error('Start sync monitor error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to start synchronization monitoring'
    });
  }
});

// @route   POST /api/shifts/sync-stop
// @desc    Stop the synchronization monitoring
// @access  Private
router.post('/sync-stop', auth, async (req, res) => {
  try {
    shiftSyncMonitor.stop();

    res.json({
      success: true,
      data: shiftSyncMonitor.getStatus(),
      message: 'Synchronization monitoring stopped'
    });

  } catch (error) {
    console.error('Stop sync monitor error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to stop synchronization monitoring'
    });
  }
});

// @route   POST /api/shifts/sync-clear-issues
// @desc    Clear all recorded synchronization issues
// @access  Private
router.post('/sync-clear-issues', auth, async (req, res) => {
  try {
    shiftSyncMonitor.clearIssues();

    res.json({
      success: true,
      data: shiftSyncMonitor.getStatus(),
      message: 'Synchronization issues cleared'
    });

  } catch (error) {
    console.error('Clear sync issues error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to clear synchronization issues'
    });
  }
});

// @route   GET /api/shifts/:id
// @desc    Get shift by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(`
      SELECT
        ds.*,
        dt.truck_number,
        dt.license_plate,
        d.full_name as driver_name,
        d.employee_id
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
      WHERE ds.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve shift'
    });
  }
});

// @route   POST /api/shifts/auto-activate
// @desc    Auto-activate shifts based on current date/time
// @access  Private
router.post('/auto-activate', auth, async (req, res) => {
  try {
    console.log('SHIFT_AUTO_ACTIVATE', 'Starting auto-activation process');

    // Call the enhanced auto-activation function
    const result = await query('SELECT auto_activate_shifts_enhanced() as activated_count');
    const activatedCount = result.rows[0].activated_count;

    console.log('SHIFT_AUTO_ACTIVATE_SUCCESS', 'Auto-activation completed', {
      activated_count: activatedCount
    });

    // Get details of newly activated shifts
    const activatedShifts = await query(`
      SELECT
        ds.id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        ds.shift_date,
        ds.start_time,
        ds.end_time,
        dt.truck_number,
        d.full_name as driver_name,
        d.employee_id
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
      WHERE ds.status = 'active'
        AND ds.updated_at >= CURRENT_TIMESTAMP - interval '1 minute'
      ORDER BY ds.updated_at DESC
    `);

    res.json({
      success: true,
      message: `Auto-activation completed. ${activatedCount} shift(s) activated.`,
      data: {
        activated_count: activatedCount,
        activated_shifts: activatedShifts.rows
      }
    });

  } catch (error) {
    console.error('Auto-activation error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to auto-activate shifts'
    });
  }
});

// @route   POST /api/shifts/schedule-auto-activation
// @desc    Schedule automatic activation for shifts (can be called periodically)
// @access  Private
router.post('/schedule-auto-activation', auth, async (req, res) => {
  try {
    console.log('SHIFT_SCHEDULE_AUTO_ACTIVATE', 'Starting scheduled auto-activation');

    // Auto-activate shifts that should be starting now
    const activationResult = await query(`
      UPDATE driver_shifts
      SET status = 'active', updated_at = CURRENT_TIMESTAMP
      WHERE status = 'scheduled'
          AND shift_date = CURRENT_DATE
          AND CURRENT_TIME >= start_time
          AND CURRENT_TIME < CASE
              WHEN end_time < start_time
              THEN end_time + interval '24 hours'
              ELSE end_time
          END
      RETURNING id, truck_id, driver_id, shift_type
    `);

    const activatedShifts = activationResult.rows;
    console.log('SHIFT_SCHEDULE_AUTO_ACTIVATE_SUCCESS', 'Scheduled auto-activation completed', {
      activated_count: activatedShifts.length,
      activated_shifts: activatedShifts
    });

    // Conditional auto-completion: Only complete shifts when end_date equals current date AND current time exceeds end_time
    const completionResult = await query(`
      UPDATE driver_shifts
      SET status = 'completed', updated_at = CURRENT_TIMESTAMP
      WHERE status = 'active'
          AND end_date = CURRENT_DATE
          AND CURRENT_TIME > CASE
              WHEN end_time < start_time
              THEN end_time + interval '24 hours'
              ELSE end_time
          END
      RETURNING id, truck_id, driver_id, shift_type, end_date
    `);

    const completedShifts = completionResult.rows;
    console.log('SHIFT_CONDITIONAL_AUTO_COMPLETE', 'Conditional auto-completion completed', {
      completed_count: completedShifts.length,
      completed_shifts: completedShifts,
      condition: 'end_date equals current date AND current time exceeds end_time'
    });

    res.json({
      success: true,
      message: `Scheduled activation completed. ${activatedShifts.length} shift(s) activated, ${completedShifts.length} shift(s) completed.`,
      data: {
        activated_count: activatedShifts.length,
        completed_count: completedShifts.length,
        activated_shifts: activatedShifts,
        completed_shifts: completedShifts
      }
    });

  } catch (error) {
    console.error('Scheduled auto-activation error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to perform scheduled shift activation'
    });
  }
});

// Note: Delete endpoint moved below to handle foreign key constraints properly

// @route   PUT /api/shifts/:id
// @desc    Update shift
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Determine which schema to use based on request content
    const isStatusOnlyUpdate = Object.keys(req.body).length === 1 && req.body.status;
    const schema = isStatusOnlyUpdate ? statusUpdateSchema : updateShiftSchema;

    console.log('SHIFT_UPDATE_VALIDATION', 'Validating shift update', {
      shift_id: id,
      request_body: req.body,
      is_status_only: isStatusOnlyUpdate,
      schema_used: isStatusOnlyUpdate ? 'statusUpdateSchema' : 'updateShiftSchema'
    });

    // Validate input with appropriate schema
    const { error } = schema.validate(req.body);
    if (error) {
      console.log('SHIFT_UPDATE_VALIDATION_ERROR', 'Validation failed', {
        shift_id: id,
        error: error.details[0].message,
        request_body: req.body,
        schema_used: isStatusOnlyUpdate ? 'statusUpdateSchema' : 'updateShiftSchema'
      });

      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message,
        details: {
          field: error.details[0].path?.join('.'),
          type: error.details[0].type,
          is_status_only_update: isStatusOnlyUpdate
        }
      });
    }

    // Check if shift exists
    const existingShift = await query('SELECT * FROM driver_shifts WHERE id = $1', [id]);
    if (existingShift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined) {
        updateFields.push(`${key} = $${paramIndex}`);
        updateValues.push(req.body[key]);
        paramIndex++;
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(id);

    const updateQuery = `
      UPDATE driver_shifts 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await query(updateQuery, updateValues);

    // Send WebSocket notification for status changes
    if (req.body.status && req.body.status !== existingShift.status) {
      broadcast({
        type: 'shift_status_changed',
        title: 'Shift Status Updated',
        message: `Shift ${existingShift.shift_type} for Truck ${existingShift.truck_id} changed from ${existingShift.status} to ${req.body.status}`,
        data: {
          shift_id: id,
          truck_id: existingShift.truck_id,
          driver_id: existingShift.driver_id,
          previous_status: existingShift.status,
          new_status: req.body.status,
          shift_date: existingShift.shift_date,
          shift_type: existingShift.shift_type
        },
        timestamp: new Date().toISOString(),
        priority: 'medium'
      });
    }

    console.log('SHIFT_UPDATE_SUCCESS', 'Shift updated successfully', {
      shift_id: id,
      updated_fields: Object.keys(req.body),
      previous_status: existingShift.status,
      new_status: req.body.status || existingShift.status
    });

    res.json({
      success: true,
      message: 'Shift updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update shift'
    });
  }
});

// @route   PATCH /api/shifts/:id/cancel
// @desc    Cancel shift (soft delete by setting status to cancelled)
// @access  Private
router.patch('/:id/cancel', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if shift exists
    const existingShift = await query('SELECT * FROM driver_shifts WHERE id = $1', [id]);
    if (existingShift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    // Soft delete by updating status
    const result = await query(`
      UPDATE driver_shifts
      SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id]);

    res.json({
      success: true,
      message: 'Shift cancelled successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Cancel shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to cancel shift'
    });
  }
});

// @route   PATCH /api/shifts/:id/complete
// @desc    Complete shift (set status to completed)
// @access  Private
router.patch('/:id/complete', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if shift exists
    const existingShift = await query('SELECT * FROM driver_shifts WHERE id = $1', [id]);
    if (existingShift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    // Update status to completed
    const result = await query(`
      UPDATE driver_shifts
      SET status = 'completed', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id]);

    res.json({
      success: true,
      message: 'Shift completed successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Complete shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to complete shift'
    });
  }
});

// @route   DELETE /api/shifts/:id
// @desc    Delete shift permanently (enhanced to handle related shifts)
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { delete_related = false } = req.query; // Option to delete related shifts

    // Check if shift exists
    const existingShift = await query('SELECT * FROM driver_shifts WHERE id = $1', [id]);
    if (existingShift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    const shift = existingShift.rows[0];

    // Find related shifts if this is part of a multi-day range
    let relatedShifts = [];
    if (delete_related === 'true' && shift.recurrence_pattern === 'custom' && shift.start_date && shift.end_date) {
      const relatedShiftsResult = await query(`
        SELECT id, shift_date, start_date, end_date, status, shift_type
        FROM driver_shifts
        WHERE truck_id = $1
          AND driver_id = $2
          AND start_date = $3
          AND end_date = $4
          AND recurrence_pattern = 'custom'
          AND shift_type = $5
          AND ABS(EXTRACT(EPOCH FROM (created_at - $6))) < 60
        ORDER BY shift_date
      `, [shift.truck_id, shift.driver_id, shift.start_date, shift.end_date, shift.shift_type, shift.created_at]);

      relatedShifts = relatedShiftsResult.rows;
      console.log('SHIFT_DELETE_RELATED', 'Found related shifts', {
        main_shift_id: id,
        related_count: relatedShifts.length,
        related_ids: relatedShifts.map(s => s.id)
      });
    }

    // Check if shift can be deleted - allow deletion of any shift except those with active trips
    // (using the shift variable already declared above)

    // For active shifts, check if there are ongoing trips
    if (shift.status === 'active') {
      const activeTripsCheck = await query(`
        SELECT COUNT(*) as active_count
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = $1
          AND tl.status NOT IN ('trip_completed', 'stopped', 'cancelled')
          AND DATE(tl.created_at) = $2
      `, [shift.truck_id, shift.shift_date]);

      if (parseInt(activeTripsCheck.rows[0].active_count) > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Cannot delete active shift with ongoing trips. Please complete or stop active trips first.'
        });
      }
    }

    // Check for foreign key constraints (trip logs referencing this shift)
    const tripLogsCheck = await query('SELECT COUNT(*) as count FROM trip_logs WHERE performed_by_shift_id = $1', [id]);
    const tripLogCount = parseInt(tripLogsCheck.rows[0].count);

    if (tripLogCount > 0) {
      // Cannot delete due to foreign key constraint - cancel instead
      await query(`
        UPDATE driver_shifts
        SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [id]);

      return res.json({
        success: true,
        message: `Shift cancelled instead of deleted (${tripLogCount} trip logs reference this shift)`,
        action: 'cancelled'
      });
    }

    // Enhanced deletion logic
    const deletionResults = {
      deleted_shifts: [],
      cancelled_shifts: [],
      failed_shifts: []
    };

    const shiftsToDelete = relatedShifts.length > 0 ? relatedShifts : [shift];

    for (const shiftToDelete of shiftsToDelete) {
      try {
        // Check if this specific shift can be deleted
        const tripLogCount = await query(`
          SELECT COUNT(*) as count FROM trip_logs
          WHERE performed_by_shift_id = $1
        `, [shiftToDelete.id]);

        if (parseInt(tripLogCount.rows[0].count) > 0) {
          // Cancel instead of delete if there are trip logs
          await query(`
            UPDATE driver_shifts
            SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [shiftToDelete.id]);

          deletionResults.cancelled_shifts.push({
            id: shiftToDelete.id,
            shift_date: shiftToDelete.shift_date,
            reason: 'Has associated trip logs - cancelled instead'
          });
        } else {
          // Safe to delete
          await query('DELETE FROM driver_shifts WHERE id = $1', [shiftToDelete.id]);

          deletionResults.deleted_shifts.push({
            id: shiftToDelete.id,
            shift_date: shiftToDelete.shift_date,
            shift_type: shiftToDelete.shift_type
          });
        }
      } catch (error) {
        deletionResults.failed_shifts.push({
          id: shiftToDelete.id,
          reason: error.message
        });
      }
    }

    const totalProcessed = deletionResults.deleted_shifts.length +
                          deletionResults.cancelled_shifts.length +
                          deletionResults.failed_shifts.length;

    res.json({
      success: true,
      message: relatedShifts.length > 0
        ? `Processed ${totalProcessed} related shifts: ${deletionResults.deleted_shifts.length} deleted, ${deletionResults.cancelled_shifts.length} cancelled`
        : 'Shift deleted permanently',
      data: deletionResults
    });

  } catch (error) {
    console.error('Delete shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete shift'
    });
  }
});

// @route   POST /api/shifts/handover
// @desc    Create shift handover
// @access  Private
router.post('/handover', auth, async (req, res) => {
  try {
    const {
      truck_id,
      outgoing_shift_id,
      incoming_shift_id,
      active_trip_id = null,
      handover_notes = '',
      fuel_level = null,
      vehicle_condition = ''
    } = req.body;

    // Validate required fields
    if (!truck_id || !outgoing_shift_id || !incoming_shift_id) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'truck_id, outgoing_shift_id, and incoming_shift_id are required'
      });
    }

    // Get trip context if active trip exists
    let tripContext = null;
    if (active_trip_id) {
      const tripQuery = `
        SELECT
          tl.id,
          tl.status,
          tl.actual_loading_location_id,
          tl.actual_unloading_location_id,
          a.loading_location_id,
          a.unloading_location_id
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE tl.id = $1
      `;

      const tripResult = await query(tripQuery, [active_trip_id]);
      if (tripResult.rows.length > 0) {
        tripContext = tripResult.rows[0];
      }
    }

    // Create handover record
    const handoverResult = await query(`
      INSERT INTO shift_handovers (
        truck_id, outgoing_shift_id, incoming_shift_id,
        active_trip_id, trip_status_at_handover,
        location_at_handover, handover_notes,
        fuel_level, vehicle_condition, approved_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      truck_id,
      outgoing_shift_id,
      incoming_shift_id,
      active_trip_id,
      tripContext?.status || null,
      tripContext?.actual_loading_location_id || tripContext?.loading_location_id || null,
      handover_notes,
      fuel_level,
      vehicle_condition,
      req.user.id
    ]);

    // Update shift statuses
    await query(`
      UPDATE driver_shifts
      SET status = 'completed',
          handover_completed_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [outgoing_shift_id]);

    await query(`
      UPDATE driver_shifts
      SET status = 'active',
          previous_shift_id = $1,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [outgoing_shift_id, incoming_shift_id]);

    res.status(201).json({
      success: true,
      message: 'Shift handover completed successfully',
      data: handoverResult.rows[0]
    });

  } catch (error) {
    console.error('Shift handover error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to complete shift handover'
    });
  }
});

// @route   GET /api/shifts/handovers
// @desc    Get shift handovers with filtering
// @access  Private
router.get('/handovers', auth, async (req, res) => {
  try {
    const { truck_id, start_date, end_date } = req.query;

    let whereConditions = ['1=1'];
    let queryParams = [];
    let paramIndex = 1;

    if (truck_id) {
      whereConditions.push(`sh.truck_id = $${paramIndex}`);
      queryParams.push(truck_id);
      paramIndex++;
    }

    if (start_date && end_date) {
      whereConditions.push(`sh.handover_time BETWEEN $${paramIndex} AND $${paramIndex + 1}`);
      queryParams.push(start_date, end_date);
      paramIndex += 2;
    }

    const handoversQuery = `
      SELECT
        sh.*,
        dt.truck_number,
        d1.full_name as outgoing_driver_name,
        d2.full_name as incoming_driver_name,
        tl.trip_number,
        l.name as location_name,
        u.full_name as approved_by_name
      FROM shift_handovers sh
      JOIN dump_trucks dt ON sh.truck_id = dt.id
      JOIN driver_shifts ds1 ON sh.outgoing_shift_id = ds1.id
      JOIN driver_shifts ds2 ON sh.incoming_shift_id = ds2.id
      JOIN drivers d1 ON ds1.driver_id = d1.id
      JOIN drivers d2 ON ds2.driver_id = d2.id
      LEFT JOIN trip_logs tl ON sh.active_trip_id = tl.id
      LEFT JOIN locations l ON sh.location_at_handover = l.id
      LEFT JOIN users u ON sh.approved_by = u.id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY sh.handover_time DESC
    `;

    const result = await query(handoversQuery, queryParams);

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('Get handovers error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve handovers'
    });
  }
});

// @route   POST /api/shifts/activate/:id
// @desc    Manually activate a shift
// @access  Private
router.post('/activate/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if shift exists and is scheduled
    const shift = await query(`
      SELECT * FROM driver_shifts
      WHERE id = $1 AND status = 'scheduled'
    `, [id]);

    if (shift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Scheduled shift not found'
      });
    }

    // Deactivate any currently active shifts for this truck
    await query(`
      UPDATE driver_shifts
      SET status = 'completed', updated_at = CURRENT_TIMESTAMP
      WHERE truck_id = $1 AND status = 'active'
    `, [shift.rows[0].truck_id]);

    // Activate the shift
    const result = await query(`
      UPDATE driver_shifts
      SET status = 'active', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id]);

    res.json({
      success: true,
      message: 'Shift activated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Activate shift error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to activate shift'
    });
  }
});

// @route   GET /api/shifts/current-drivers
// @desc    Get current active drivers for display purposes only (does not affect workflow)
// @access  Private
router.get('/current-drivers', auth, async (req, res) => {
  try {
    const { truck_ids } = req.query;

    if (!truck_ids) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'truck_ids parameter is required (comma-separated list)'
      });
    }

    // Parse truck IDs
    const truckIdArray = truck_ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

    if (truckIdArray.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No valid truck IDs provided'
      });
    }

    // Get current drivers for display
    const currentDrivers = await ShiftDisplayHelper.getCurrentDriversForDisplay(truckIdArray);

    res.json({
      success: true,
      data: currentDrivers,
      note: 'This data is for display purposes only and does not affect the 4-phase workflow'
    });

  } catch (error) {
    console.error('Get current drivers error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve current drivers'
    });
  }
});

// @route   GET /api/shifts/current-driver/:truck_id
// @desc    Get current active driver for a specific truck (display only)
// @access  Private
router.get('/current-driver/:truck_id', auth, async (req, res) => {
  try {
    const { truck_id } = req.params;

    if (!truck_id || isNaN(parseInt(truck_id))) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Valid truck_id is required'
      });
    }

    // Get current driver for display
    const currentDriver = await ShiftDisplayHelper.getCurrentDriverForDisplay(parseInt(truck_id));

    res.json({
      success: true,
      data: currentDriver,
      note: 'This data is for display purposes only and does not affect the 4-phase workflow'
    });

  } catch (error) {
    console.error('Get current driver error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve current driver'
    });
  }
});

// @route   GET /api/shifts/debug/:truck_id
// @desc    Debug endpoint to check existing shifts for a truck
// @access  Private
router.get('/debug/:truck_id', auth, async (req, res) => {
  try {
    const { truck_id } = req.params;

    // Get all shifts for this truck
    const shifts = await query(`
      SELECT
        ds.*,
        d.full_name as driver_name,
        dt.truck_number
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      WHERE ds.truck_id = $1
      ORDER BY ds.shift_date DESC, ds.start_time ASC
    `, [truck_id]);

    // Get truck info
    const truckInfo = await query(`
      SELECT id, truck_number, license_plate
      FROM dump_trucks
      WHERE id = $1
    `, [truck_id]);

    res.json({
      success: true,
      data: {
        truck: truckInfo.rows[0] || null,
        shifts: shifts.rows,
        totalShifts: shifts.rows.length
      }
    });

  } catch (error) {
    console.error('Debug shifts error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to debug shifts'
    });
  }
});

// @route   DELETE /api/shifts/group/:id
// @desc    Delete entire shift group (all related shifts created together)
// @access  Private
router.delete('/group/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Get the main shift to find its group
    const mainShift = await query('SELECT * FROM driver_shifts WHERE id = $1', [id]);
    if (mainShift.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Shift not found'
      });
    }

    const shift = mainShift.rows[0];

    // Find all shifts in the same group (created together)
    const groupShifts = await query(`
      SELECT id, shift_date, start_date, end_date, status, shift_type, created_at
      FROM driver_shifts
      WHERE truck_id = $1
        AND driver_id = $2
        AND start_date = $3
        AND end_date = $4
        AND shift_type = $5
        AND ABS(EXTRACT(EPOCH FROM (created_at - $6))) < 60
      ORDER BY COALESCE(shift_date, start_date)
    `, [shift.truck_id, shift.driver_id, shift.start_date, shift.end_date, shift.shift_type, shift.created_at]);

    console.log('SHIFT_GROUP_DELETE', 'Deleting shift group', {
      main_shift_id: id,
      group_size: groupShifts.rows.length,
      shift_ids: groupShifts.rows.map(s => s.id)
    });

    const deletionResults = {
      deleted_shifts: [],
      cancelled_shifts: [],
      failed_shifts: []
    };

    // Process each shift in the group
    for (const groupShift of groupShifts.rows) {
      try {
        // Check if this shift has trip logs
        const tripLogCount = await query(`
          SELECT COUNT(*) as count FROM trip_logs
          WHERE performed_by_shift_id = $1
        `, [groupShift.id]);

        if (parseInt(tripLogCount.rows[0].count) > 0) {
          // Cancel instead of delete
          await query(`
            UPDATE driver_shifts
            SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [groupShift.id]);

          deletionResults.cancelled_shifts.push({
            id: groupShift.id,
            shift_date: groupShift.shift_date,
            reason: 'Has associated trip logs'
          });
        } else {
          // Safe to delete
          await query('DELETE FROM driver_shifts WHERE id = $1', [groupShift.id]);

          deletionResults.deleted_shifts.push({
            id: groupShift.id,
            shift_date: groupShift.shift_date,
            shift_type: groupShift.shift_type
          });
        }
      } catch (error) {
        deletionResults.failed_shifts.push({
          id: groupShift.id,
          reason: error.message
        });
      }
    }

    res.json({
      success: true,
      message: `Shift group processed: ${deletionResults.deleted_shifts.length} deleted, ${deletionResults.cancelled_shifts.length} cancelled, ${deletionResults.failed_shifts.length} failed`,
      data: {
        group_size: groupShifts.rows.length,
        ...deletionResults
      }
    });

  } catch (error) {
    console.error('Delete shift group error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete shift group'
    });
  }
});

// @route   DELETE /api/shifts/bulk
// @desc    Bulk delete shifts with intelligent handling
// @access  Private
router.delete('/bulk', auth, async (req, res) => {
  try {
    const { shift_ids } = req.body;

    if (!shift_ids || !Array.isArray(shift_ids) || shift_ids.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'shift_ids array is required'
      });
    }

    const results = {
      deleted: [],
      cancelled: [],
      failed: []
    };

    for (const shiftId of shift_ids) {
      try {
        // Check if shift exists
        const existingShift = await query(`
          SELECT id, status, truck_id, driver_id, shift_date, shift_type
          FROM driver_shifts
          WHERE id = $1
        `, [shiftId]);

        if (existingShift.rows.length === 0) {
          results.failed.push({
            id: shiftId,
            reason: 'Shift not found'
          });
          continue;
        }

        const shift = existingShift.rows[0];

        // Check for active trips if shift is active
        if (shift.status === 'active') {
          const activeTripsCheck = await query(`
            SELECT COUNT(*) as active_count
            FROM trip_logs tl
            JOIN assignments a ON tl.assignment_id = a.id
            WHERE a.truck_id = $1
              AND tl.status NOT IN ('trip_completed', 'stopped', 'cancelled')
              AND DATE(tl.created_at) = $2
          `, [shift.truck_id, shift.shift_date]);

          if (parseInt(activeTripsCheck.rows[0].active_count) > 0) {
            results.failed.push({
              id: shiftId,
              reason: 'Active shift with ongoing trips'
            });
            continue;
          }
        }

        // Check for foreign key constraints
        const tripLogsCheck = await query('SELECT COUNT(*) as count FROM trip_logs WHERE performed_by_shift_id = $1', [shiftId]);
        const tripLogCount = parseInt(tripLogsCheck.rows[0].count);

        if (tripLogCount > 0) {
          // Cancel instead of delete
          await query(`
            UPDATE driver_shifts
            SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [shiftId]);

          results.cancelled.push({
            id: shiftId,
            shift_date: shift.shift_date,
            shift_type: shift.shift_type,
            reason: `${tripLogCount} trip logs reference this shift`
          });
        } else {
          // Safe to delete
          await query('DELETE FROM driver_shifts WHERE id = $1', [shiftId]);

          results.deleted.push({
            id: shiftId,
            shift_date: shift.shift_date,
            shift_type: shift.shift_type
          });
        }

      } catch (error) {
        results.failed.push({
          id: shiftId,
          reason: error.message
        });
      }
    }

    console.log('BULK_SHIFT_DELETE_SUCCESS', 'Bulk shift operation completed', {
      total_requested: shift_ids.length,
      deleted_count: results.deleted.length,
      cancelled_count: results.cancelled.length,
      failed_count: results.failed.length
    });

    res.json({
      success: true,
      message: `Bulk operation completed: ${results.deleted.length} deleted, ${results.cancelled.length} cancelled, ${results.failed.length} failed`,
      results
    });

  } catch (error) {
    console.error('Bulk delete shifts error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to perform bulk delete operation'
    });
  }
});

module.exports = router;
