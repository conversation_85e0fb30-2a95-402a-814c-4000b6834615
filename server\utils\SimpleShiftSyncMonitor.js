const { query } = require('../config/database');

class SimpleShiftSyncMonitor {
  constructor() {
    this.isRunning = false;
    this.monitoringInterval = null;
    this.checkInterval = 30000; // Check every 30 seconds
    this.lastCheck = null;
    this.issues = [];
  }

  /**
   * Start the synchronization monitoring
   */
  start() {
    if (this.isRunning) {
      console.log('SHIFT_SYNC_MONITOR', 'Monitor already running');
      return;
    }

    this.isRunning = true;
    console.log('SHIFT_SYNC_MONITOR', 'Starting shift synchronization monitoring');

    // Run initial check
    this.performSyncCheck();

    // Set up periodic monitoring
    this.monitoringInterval = setInterval(() => {
      this.performSyncCheck();
    }, this.checkInterval);
  }

  /**
   * Stop the synchronization monitoring
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    console.log('SHIFT_SYNC_MONITOR', 'Stopped shift synchronization monitoring');
  }

  /**
   * Perform synchronization check between systems
   */
  async performSyncCheck() {
    try {
      this.lastCheck = new Date();
      const issues = [];

      // 1. Check for scheduled shifts that should be active
      const scheduledShiftsResult = await query(`
        SELECT
          ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status,
          dt.truck_number, d.full_name,
          ds.start_time, ds.end_time,
          ds.start_date, ds.end_date
        FROM driver_shifts ds
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.status = 'scheduled'
          AND CURRENT_DATE BETWEEN COALESCE(ds.start_date, ds.shift_date) AND COALESCE(ds.end_date, ds.shift_date)
          AND (
            -- Handle overnight shifts (end_time < start_time)
            (ds.end_time < ds.start_time AND
             (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
            OR
            -- Handle same-day shifts (end_time >= start_time)
            (ds.end_time >= ds.start_time AND
             CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
          )
      `);

      if (scheduledShiftsResult.rows.length > 0) {
        issues.push({
          type: 'scheduled_shifts_need_activation',
          severity: 'medium',
          count: scheduledShiftsResult.rows.length,
          shifts: scheduledShiftsResult.rows,
          message: `${scheduledShiftsResult.rows.length} scheduled shift(s) should be auto-activated`
        });

        // Auto-fix: Activate the shifts
        await this.autoActivateShifts(scheduledShiftsResult.rows);
      }

      // 2. Check for orphaned active shifts (active shifts without time validity)
      const orphanedShiftsResult = await query(`
        SELECT
          ds.id, ds.truck_id, ds.driver_id, ds.shift_type,
          dt.truck_number, d.full_name,
          ds.start_time, ds.end_time,
          ds.start_date, ds.end_date
        FROM driver_shifts ds
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.status = 'active'
          AND CURRENT_DATE BETWEEN COALESCE(ds.start_date, ds.shift_date) AND COALESCE(ds.end_date, ds.shift_date)
          AND NOT (
            -- Handle overnight shifts (end_time < start_time)
            (ds.end_time < ds.start_time AND
             (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
            OR
            -- Handle same-day shifts (end_time >= start_time)
            (ds.end_time >= ds.start_time AND
             CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
          )
      `);

      if (orphanedShiftsResult.rows.length > 0) {
        issues.push({
          type: 'orphaned_active_shifts',
          severity: 'high',
          count: orphanedShiftsResult.rows.length,
          shifts: orphanedShiftsResult.rows,
          message: `${orphanedShiftsResult.rows.length} active shift(s) are outside their valid time range`
        });

        // Auto-fix: Complete the orphaned shifts
        await this.completeOrphanedShifts(orphanedShiftsResult.rows);
      }

      // Update issues list
      this.issues = issues;

      // Log summary
      if (issues.length > 0) {
        console.log('SHIFT_SYNC_MONITOR', `Found ${issues.length} synchronization issue(s):`);
        issues.forEach(issue => {
          console.log(`  - ${issue.type}: ${issue.message} (${issue.severity})`);
        });
      }

    } catch (error) {
      console.error('SHIFT_SYNC_MONITOR', 'Error during sync check:', error);
      this.issues.push({
        type: 'monitor_error',
        severity: 'high',
        message: `Monitoring error: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Auto-activate shifts that should be active
   */
  async autoActivateShifts(shifts) {
    try {
      for (const shift of shifts) {
        // Use transaction to prevent race conditions
        await query('BEGIN');
        
        try {
          // 1. Deactivate any currently active shifts for this truck
          await query(`
            UPDATE driver_shifts
            SET status = 'completed', updated_at = CURRENT_TIMESTAMP
            WHERE truck_id = $1 AND status = 'active' AND id != $2
            RETURNING id
          `, [shift.truck_id, shift.id]);

          // 2. Activate the scheduled shift
          await query(`
            UPDATE driver_shifts
            SET status = 'active', updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            RETURNING id
          `, [shift.id]);

          // 3. Sync with Assignment Management
          await this.syncWithAssignmentManagement(shift.id, 'active');

          await query('COMMIT');
          console.log('SHIFT_SYNC_MONITOR', `Auto-activated shift ${shift.id} for ${shift.truck_number} - ${shift.full_name}`);
        } catch (innerError) {
          await query('ROLLBACK');
          throw innerError;
        }
      }
    } catch (error) {
      console.error('SHIFT_SYNC_MONITOR', 'Error auto-activating shifts:', error);
    }
  }

  /**
   * Complete orphaned active shifts
   */
  async completeOrphanedShifts(shifts) {
    try {
      for (const shift of shifts) {
        // Use transaction to prevent race conditions
        await query('BEGIN');
        
        try {
          // 1. Complete the shift
          await query(`
            UPDATE driver_shifts
            SET status = 'completed', updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            RETURNING id
          `, [shift.id]);

          // 2. Sync with Assignment Management
          await this.syncWithAssignmentManagement(shift.id, 'completed');

          await query('COMMIT');
          console.log('SHIFT_SYNC_MONITOR', `Completed orphaned shift ${shift.id} for ${shift.truck_number} - ${shift.full_name}`);
        } catch (innerError) {
          await query('ROLLBACK');
          throw innerError;
        }
      }
    } catch (error) {
      console.error('SHIFT_SYNC_MONITOR', 'Error completing orphaned shifts:', error);
    }
  }

  /**
   * Get current synchronization status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastCheck: this.lastCheck,
      checkInterval: this.checkInterval,
      issueCount: this.issues.length,
      issues: this.issues,
      uptime: this.isRunning ? Date.now() - (this.lastCheck?.getTime() || Date.now()) : 0
    };
  }

  /**
   * Force a manual synchronization check
   */
  async forceCheck() {
    console.log('SHIFT_SYNC_MONITOR', 'Forcing manual synchronization check');
    await this.performSyncCheck();
    return this.getStatus();
  }

  /**
   * Clear all recorded issues
   */
  clearIssues() {
    this.issues = [];
    console.log('SHIFT_SYNC_MONITOR', 'Cleared all recorded issues');
  }

  /**
   * Synchronize shift status with Assignment Management system
   */
  async syncWithAssignmentManagement(shiftId, newStatus) {
    try {
      // Add your Assignment Management API call here
      // Example:
      // await assignmentManagementClient.updateShiftStatus(shiftId, newStatus);
      console.log('SHIFT_SYNC_MONITOR', `Synced shift ${shiftId} status (${newStatus}) with Assignment Management`);
    } catch (error) {
      console.error('SHIFT_SYNC_MONITOR', 'Failed to sync with Assignment Management:', error);
      throw error;
    }
  }
}

// Export singleton instance
const simpleShiftSyncMonitor = new SimpleShiftSyncMonitor();
module.exports = simpleShiftSyncMonitor;
