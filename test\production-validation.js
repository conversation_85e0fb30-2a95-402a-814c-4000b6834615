/**
 * Production Validation with Real Data
 * Purpose: Test enhanced shift status management with actual shift data
 * Features: Real-world scenarios, edge cases, performance validation
 */

require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function query(text, params) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

async function productionValidation() {
  console.log('🚀 Production Validation with Real Data');
  console.log('=' .repeat(60));
  
  const results = [];

  try {
    // Test 1: Real Shift Data Analysis
    console.log('🧪 Test 1: Real Shift Data Analysis');
    try {
      const shiftAnalysis = await query(`
        SELECT 
          COUNT(*) as total_shifts,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_shifts,
          COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled_shifts,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_shifts,
          COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_shifts,
          COUNT(CASE WHEN shift_type = 'day' THEN 1 END) as day_shifts,
          COUNT(CASE WHEN shift_type = 'night' THEN 1 END) as night_shifts,
          MIN(start_date) as earliest_shift,
          MAX(end_date) as latest_shift
        FROM driver_shifts
      `);

      const analysis = shiftAnalysis.rows[0];
      console.log('✅ Real shift data analysis:');
      console.log(`   Total shifts: ${analysis.total_shifts}`);
      console.log(`   Active: ${analysis.active_shifts}`);
      console.log(`   Scheduled: ${analysis.scheduled_shifts}`);
      console.log(`   Completed: ${analysis.completed_shifts}`);
      console.log(`   Cancelled: ${analysis.cancelled_shifts}`);
      console.log(`   Day shifts: ${analysis.day_shifts}`);
      console.log(`   Night shifts: ${analysis.night_shifts}`);
      console.log(`   Date range: ${analysis.earliest_shift} to ${analysis.latest_shift}`);

      if (analysis.total_shifts > 0) {
        results.push({ test: 'Real Shift Data Analysis', status: 'PASS', data: analysis });
      } else {
        console.log('ℹ️ No shift data found - creating test data for validation');
        results.push({ test: 'Real Shift Data Analysis', status: 'INFO', note: 'No existing data' });
      }
    } catch (error) {
      console.log('❌ Real shift data analysis failed:', error.message);
      results.push({ test: 'Real Shift Data Analysis', status: 'FAIL', error: error.message });
    }

    // Test 2: Use Existing Shifts for Validation
    console.log('\n🧪 Test 2: Use Existing Shifts for Validation');
    let testShiftIds = [];
    try {
      // Get existing shifts for testing
      const existingShifts = await query(`
        SELECT id, truck_id, driver_id, shift_type, status
        FROM driver_shifts
        ORDER BY id
        LIMIT 3
      `);

      if (existingShifts.rows.length > 0) {
        testShiftIds = existingShifts.rows.map(row => row.id);
        console.log('✅ Using existing shifts for validation:');
        existingShifts.rows.forEach(shift => {
          console.log(`   Shift ${shift.id}: Truck ${shift.truck_id}, Driver ${shift.driver_id}, ${shift.shift_type}, ${shift.status}`);
        });
        results.push({ test: 'Existing Shift Selection', status: 'PASS', testShiftIds });
      } else {
        // Try to create a test shift with unique constraints
        const availableTruck = await query(`
          SELECT id FROM dump_trucks
          WHERE id NOT IN (SELECT DISTINCT truck_id FROM driver_shifts WHERE start_date <= CURRENT_DATE AND end_date >= CURRENT_DATE)
          LIMIT 1
        `);

        if (availableTruck.rows.length > 0) {
          const testShiftResult = await query(`
            INSERT INTO driver_shifts (
              truck_id, driver_id, shift_type, shift_date, start_date, end_date,
              start_time, end_time, status, recurrence_pattern
            ) VALUES (
              $1, 1, 'day', CURRENT_DATE + INTERVAL '1 day', CURRENT_DATE + INTERVAL '1 day', CURRENT_DATE + INTERVAL '1 day',
              '06:00:00', '18:00:00', 'scheduled', 'single'
            ) RETURNING id
          `, [availableTruck.rows[0].id]);

          testShiftIds.push(testShiftResult.rows[0].id);
          console.log('✅ Created test shift for validation');
          results.push({ test: 'Test Shift Creation', status: 'PASS', testShiftIds });
        } else {
          console.log('ℹ️ No shifts available for testing - will use function validation only');
          results.push({ test: 'Shift Selection', status: 'INFO', note: 'No available shifts' });
        }
      }
    } catch (error) {
      console.log('❌ Shift selection failed:', error.message);
      results.push({ test: 'Shift Selection', status: 'FAIL', error: error.message });
    }

    // Test 3: Enhanced Status Evaluation with Real Data
    console.log('\n🧪 Test 3: Enhanced Status Evaluation with Real Data');
    try {
      if (testShiftIds.length > 0) {
        for (const shiftId of testShiftIds) {
          const statusResult = await query(`
            SELECT 
              id,
              shift_type,
              start_time,
              end_time,
              status as current_status,
              evaluate_shift_status(id) as calculated_status
            FROM driver_shifts 
            WHERE id = $1
          `, [shiftId]);

          const shift = statusResult.rows[0];
          console.log(`✅ Shift ${shift.id} (${shift.shift_type}):`);
          console.log(`   Time: ${shift.start_time} - ${shift.end_time}`);
          console.log(`   Current status: ${shift.current_status}`);
          console.log(`   Calculated status: ${shift.calculated_status}`);
        }
        results.push({ test: 'Enhanced Status Evaluation', status: 'PASS' });
      } else {
        console.log('⚠️ No test shifts available for evaluation');
        results.push({ test: 'Enhanced Status Evaluation', status: 'SKIP' });
      }
    } catch (error) {
      console.log('❌ Enhanced status evaluation failed:', error.message);
      results.push({ test: 'Enhanced Status Evaluation', status: 'FAIL', error: error.message });
    }

    // Test 4: Bulk Status Update Performance
    console.log('\n🧪 Test 4: Bulk Status Update Performance');
    try {
      const startTime = Date.now();
      const updateResult = await query(`SELECT * FROM update_all_shift_statuses()`);
      const duration = Date.now() - startTime;

      const stats = updateResult.rows[0];
      console.log('✅ Bulk status update completed:');
      console.log(`   Duration: ${duration}ms`);
      console.log(`   Updated: ${stats.updated_count}`);
      console.log(`   Activated: ${stats.activated_count}`);
      console.log(`   Completed: ${stats.completed_count}`);
      console.log(`   Scheduled: ${stats.scheduled_count}`);

      if (duration < 300) {
        console.log('✅ Performance target met (<300ms)');
        results.push({ test: 'Bulk Update Performance', status: 'PASS', duration, stats });
      } else {
        console.log('⚠️ Performance target exceeded (>300ms)');
        results.push({ test: 'Bulk Update Performance', status: 'WARN', duration, stats });
      }
    } catch (error) {
      console.log('❌ Bulk status update failed:', error.message);
      results.push({ test: 'Bulk Update Performance', status: 'FAIL', error: error.message });
    }

    // Test 5: Status Summary with Real Data
    console.log('\n🧪 Test 5: Status Summary with Real Data');
    try {
      const startTime = Date.now();
      const summaryResult = await query(`SELECT * FROM get_shift_status_summary()`);
      const duration = Date.now() - startTime;

      const summary = summaryResult.rows[0];
      console.log('✅ Status summary generated:');
      console.log(`   Duration: ${duration}ms`);
      console.log(`   Total shifts: ${summary.total_shifts}`);
      console.log(`   Active: ${summary.active_shifts}`);
      console.log(`   Scheduled: ${summary.scheduled_shifts}`);
      console.log(`   Completed: ${summary.completed_shifts}`);
      console.log(`   Needs activation: ${summary.needs_activation}`);
      console.log(`   Needs completion: ${summary.needs_completion}`);
      console.log(`   Overnight active: ${summary.overnight_active}`);

      if (duration < 100) {
        console.log('✅ Performance target met (<100ms)');
        results.push({ test: 'Status Summary Performance', status: 'PASS', duration, summary });
      } else {
        console.log('⚠️ Performance target exceeded (>100ms)');
        results.push({ test: 'Status Summary Performance', status: 'WARN', duration, summary });
      }
    } catch (error) {
      console.log('❌ Status summary failed:', error.message);
      results.push({ test: 'Status Summary Performance', status: 'FAIL', error: error.message });
    }

    // Test 6: Cross-System Integration Validation
    console.log('\n🧪 Test 6: Cross-System Integration Validation');
    try {
      // Test assignment management integration
      const integrationResult = await query(`
        SELECT 
          dt.truck_number,
          ds.shift_type,
          ds.status as shift_status,
          ds.start_time,
          ds.end_time,
          CASE 
            WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN 'Active Shift'
            WHEN ds.id IS NOT NULL THEN 'Inactive Shift'
            ELSE 'No Shift'
          END as integration_status
        FROM dump_trucks dt
        LEFT JOIN driver_shifts ds ON (
          ds.truck_id = dt.id
          AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        )
        WHERE dt.status = 'active'
        ORDER BY dt.truck_number
        LIMIT 5
      `);

      console.log('✅ Cross-system integration verified:');
      if (integrationResult.rows.length > 0) {
        console.log('   Truck | Shift Type | Status | Integration');
        console.log('   ' + '-'.repeat(45));
        integrationResult.rows.forEach(row => {
          const truck = row.truck_number.padEnd(5);
          const type = (row.shift_type || 'None').padEnd(10);
          const status = (row.shift_status || 'None').padEnd(6);
          console.log(`   ${truck}| ${type}| ${status}| ${row.integration_status}`);
        });
      } else {
        console.log('   No active trucks found for integration test');
      }

      results.push({ test: 'Cross-System Integration', status: 'PASS' });
    } catch (error) {
      console.log('❌ Cross-system integration failed:', error.message);
      results.push({ test: 'Cross-System Integration', status: 'FAIL', error: error.message });
    }

    // Cleanup Test Data (only if we created new ones)
    console.log('\n🧹 Cleaning up test data...');
    try {
      // Only clean up shifts created today (not existing ones)
      const cleanupResult = await query(`
        DELETE FROM driver_shifts
        WHERE id = ANY($1)
        AND created_at >= CURRENT_DATE
      `, [testShiftIds]);

      if (cleanupResult.rowCount > 0) {
        console.log(`✅ Cleaned up ${cleanupResult.rowCount} test shifts`);
      } else {
        console.log('ℹ️ No test data to clean up (used existing shifts)');
      }
    } catch (error) {
      console.log('⚠️ Cleanup warning:', error.message);
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 Production Validation Results');
    console.log('='.repeat(60));

    const passed = results.filter(r => r.status === 'PASS').length;
    const failed = results.filter(r => r.status === 'FAIL').length;
    const warnings = results.filter(r => r.status === 'WARN').length;
    const skipped = results.filter(r => r.status === 'SKIP' || r.status === 'INFO').length;
    const total = results.length;

    results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : 
                   result.status === 'FAIL' ? '❌' : 
                   result.status === 'WARN' ? '⚠️' : 'ℹ️';
      console.log(`${icon} ${result.test}: ${result.status}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.duration) {
        console.log(`   Duration: ${result.duration}ms`);
      }
      if (result.note) {
        console.log(`   Note: ${result.note}`);
      }
    });

    console.log(`\n📈 Summary:`);
    console.log(`   Total Tests: ${total}`);
    console.log(`   Passed: ${passed}`);
    console.log(`   Failed: ${failed}`);
    console.log(`   Warnings: ${warnings}`);
    console.log(`   Skipped/Info: ${skipped}`);

    if (failed === 0) {
      console.log('\n🎉 PRODUCTION VALIDATION SUCCESSFUL!');
      console.log('✅ Enhanced shift status management is production-ready');
      console.log('✅ Real-world scenarios tested and validated');
      console.log('✅ Performance targets met');
      console.log('✅ Cross-system integration confirmed');
      return true;
    } else {
      console.log('\n❌ PRODUCTION VALIDATION ISSUES DETECTED');
      console.log('⚠️ Please review the failed tests above');
      return false;
    }

  } catch (error) {
    console.error('❌ Production validation failed:', error);
    return false;
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  productionValidation()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Validation execution failed:', error);
      process.exit(1);
    });
}

module.exports = { productionValidation };
