# Shift Status Management System

## Overview

This system provides comprehensive shift status management based on date and time rules. It automatically evaluates and updates shift statuses according to the following rules:

### Status Rules

1. **Active**: Current date/time is within the shift's date range AND within the defined time window
2. **Scheduled**: Current date is within the shift's date range BUT outside the time window
3. **Completed**: Current date/time is past the shift's end date and end time

## Architecture

### Database Functions
- `evaluate_shift_status(shift_id, reference_timestamp)` - Evaluates correct status for a shift
- `update_all_shift_statuses()` - Updates status for all shifts
- `update_shift_status(shift_id)` - Updates status for a specific shift

### Service Layer
- `ShiftStatusService` - Node.js service for status management
- REST API endpoints for manual updates
- <PERSON><PERSON> job for automatic updates

## Implementation

### 1. Database Migration

Run the migration to create the necessary functions:

```bash
psql -d hauling_qr_system -f database/migrations/036_create_shift_status_evaluation.sql
```

### 2. Service Usage

#### Evaluate Single Shift
```javascript
const ShiftStatusService = require('./server/services/shiftStatusService');

// Evaluate without updating
const newStatus = ShiftStatusService.evaluateShiftStatus(shiftObject);

// Update with evaluation
const result = await ShiftStatusService.updateShiftStatus(shiftId);
```

#### Update All Shifts
```javascript
const result = await ShiftStatusService.updateAllShiftStatuses();
console.log(`Updated ${result.updatedCount} shifts`);
```

### 3. REST API Endpoints

#### Update Single Shift
```http
POST /api/shift-status/update/:shiftId
Content-Type: application/json

{
  "referenceTime": "2025-07-14T14:00:00Z" // optional
}
```

#### Update All Shifts
```http
POST /api/shift-status/update-all
Content-Type: application/json

{
  "referenceTime": "2025-07-14T14:00:00Z", // optional
  "dryRun": true // optional - preview changes
}
```

#### Evaluate Shift Status
```http
GET /api/shift-status/evaluate/:shiftId?referenceTime=2025-07-14T14:00:00Z
```

#### Get Shifts Needing Updates
```http
GET /api/shift-status/needs-update?referenceTime=2025-07-14T14:00:00Z
```

### 4. Cron Job Setup

#### Manual Execution
```bash
# Update all shifts
node server/scripts/shiftStatusCron.js

# Dry run (preview changes)
node server/scripts/shiftStatusCron.js --dry-run

# Verbose output
node server/scripts/shiftStatusCron.js --verbose
```

#### Cron Schedule
Add to crontab for automatic updates every 15 minutes:
```bash
*/15 * * * * cd /path/to/project && node server/scripts/shiftStatusCron.js >> /var/log/shift-status.log 2>&1
```

## Time Window Logic

### Day Shifts
- **Active**: Current time between start_time and end_time on same day
- **Scheduled**: Current date within range but time outside window

### Night Shifts (Overnight)
- **Active**: Current time between start_time and 23:59:59 OR 00:00:00 and end_time
- **Scheduled**: Current date within range but time outside overnight window

### Examples

#### Day Shift Example
- **Shift**: 2025-07-14 to 2025-07-31, 09:00:00 to 17:00:00
- **Current**: 2025-07-14 14:00:00 → **Active**
- **Current**: 2025-07-14 08:00:00 → **Scheduled**
- **Current**: 2025-08-01 12:00:00 → **Completed**

#### Night Shift Example
- **Shift**: 2025-07-14 to 2025-07-31, 18:00:00 to 06:00:00
- **Current**: 2025-07-14 22:00:00 → **Active**
- **Current**: 2025-07-14 12:00:00 → **Scheduled**
- **Current**: 2025-07-15 02:00:00 → **Active** (overnight)
- **Current**: 2025-08-01 12:00:00 → **Completed**

## Testing

### Database Testing
```sql
-- Run test queries
\i database/test_shift_status_evaluation.sql
```

### Service Testing
```javascript
// Test with specific scenarios
const testShift = {
    start_date: new Date('2025-07-14'),
    end_date: new Date('2025-07-31'),
    start_time: '18:00:00',
    end_time: '06:00:00',
    shift_type: 'night'
};

const status = ShiftStatusService.evaluateShiftStatus(
    testShift, 
    new Date('2025-07-15T02:00:00Z')
);
console.log('Status:', status); // Should be 'active'
```

## Performance Considerations

### Database Indexes
- `idx_driver_shifts_status_date` - Optimizes status/date queries
- `idx_driver_shifts_time_range` - Optimizes time-based queries

### Batch Processing
- Updates are processed in batches to minimize database load
- Only shifts needing status changes are updated

## Error Handling

### Common Issues
1. **Invalid Date Formats**: Ensure dates are in ISO format
2. **Time Zone Issues**: All times are handled in UTC
3. **Database Connection**: Check connection string configuration

### Debugging
```javascript
// Enable verbose logging
const result = await ShiftStatusService.updateAllShiftStatuses();
console.log('Detailed updates:', result.updates);
```

## Integration Guide

### 1. Add to Express App
```javascript
const shiftStatusRoutes = require('./server/routes/shiftStatus');
app.use('/api/shift-status', shiftStatusRoutes);
```

### 2. Schedule Automatic Updates
```javascript
// In your main server file
const cron = require('node-cron');
const ShiftStatusService = require('./services/shiftStatusService');

// Run every 15 minutes
cron.schedule('*/15 * * * *', async () => {
    try {
        await ShiftStatusService.scheduleStatusUpdates();
    } catch (error) {
        console.error('Scheduled update failed:', error);
    }
});
```

### 3. Monitor Updates
```javascript
// Add logging middleware
app.use('/api/shift-status', (req, res, next) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
    next();
});
```

## Migration from Legacy System

### Before Migration
- Ensure all shifts have valid start_date, end_date, start_time, end_time
- Verify shift_type values are correct
- Backup existing shift data

### Migration Steps
1. Run database migration
2. Test with dry-run mode
3. Deploy new service
4. Schedule automatic updates
5. Monitor for issues

## Support

For issues or questions:
1. Check the logs in `/var/log/shift-status.log`
2. Review the test cases in `database/test_shift_status_evaluation.sql`
3. Use the dry-run mode to preview changes
4. Contact the development team