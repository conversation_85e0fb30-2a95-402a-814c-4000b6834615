-- Let's examine the schedule_auto_activation function logic more closely
-- This function seems to be the culprit for marking shifts as completed prematurely

-- Check if this function is being called by a cron job or scheduled task
SELECT 
    pg_get_functiondef(oid) 
FROM 
    pg_proc 
WHERE 
    proname = 'schedule_auto_activation';

-- Let's test the logic in this function for our problematic shift
-- The function has this logic for completing shifts:
-- For overnight shifts: (end_time < start_time AND CURRENT_TIME >= end_time AND CURRENT_TIME < start_time)

SELECT 
    id,
    shift_type,
    start_time,
    end_time,
    CURRENT_TIME,
    -- Test the overnight completion logic from schedule_auto_activation
    (end_time < start_time AND CURRENT_TIME >= end_time AND CURRENT_TIME < start_time) AS should_complete_by_auto_activation,
    -- Test the correct overnight completion logic
    CURRENT_TIMESTAMP > ((end_date + INTERVAL '1 day')::DATE + end_time) AS should_complete_correctly
FROM 
    driver_shifts
WHERE 
    id = 536;