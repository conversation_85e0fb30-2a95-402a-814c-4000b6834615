-- Test the debug function with the problematic shift
SELECT * FROM debug_shift_status(536);

-- Test with different reference timestamps
SELECT * FROM debug_shift_status(536, '2025-07-15 17:30:00'::TIMESTAMP);
SELECT * FROM debug_shift_status(536, '2025-07-31 06:01:00'::TIMESTAMP);
SELECT * FROM debug_shift_status(536, '2025-07-31 05:59:00'::TIMESTAMP);
SELECT * FROM debug_shift_status(536, '2025-08-01 00:00:00'::TIMESTAMP);