-- Create a monitoring function to check for status inconsistencies
CREATE OR R<PERSON>LACE FUNCTION check_shift_status_consistency()
RETURNS TABLE(
    shift_id INTEGER,
    current_status shift_status,
    expected_status shift_status,
    shift_truck_id INTEGER,
    shift_driver_id INTEGER,
    shift_type shift_type,
    start_date DATE,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    issue_description TEXT
) AS $$
DECLARE
    v_shift RECORD;
    v_expected_status shift_status;
BEGIN
    -- Check all shifts for status consistency
    FOR v_shift IN
        SELECT 
            ds.id, 
            ds.truck_id, 
            ds.driver_id, 
            ds.shift_type, 
            ds.status, 
            ds.start_date, 
            ds.end_date, 
            ds.start_time, 
            ds.end_time
        FROM driver_shifts ds
        WHERE ds.status IN ('scheduled', 'active', 'completed')
    LOOP
        -- Get the expected status using the override function
        v_expected_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP, TRUE);
        
        -- If there's a mismatch, report it
        IF v_expected_status != v_shift.status THEN
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_expected_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date,
                v_shift.start_time,
                v_shift.end_time,
                CASE 
                    WHEN v_shift.status = 'completed' AND v_expected_status != 'completed' THEN 
                        'Shift incorrectly marked as completed'
                    WHEN v_shift.status = 'scheduled' AND v_expected_status = 'active' THEN 
                        'Shift should be active but is still scheduled'
                    WHEN v_shift.status = 'active' AND v_expected_status = 'completed' THEN 
                        'Shift should be completed but is still active'
                    ELSE 
                        'Status mismatch detected'
                END;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;